@extends('layouts.app')

@section('title', 'Payment System Test Dashboard')

@section('content')
<section class="py-12 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-white mb-8">Payment System Test Dashboard</h1>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                    <h3 class="text-sm text-gray-400 mb-1">Total Payments</h3>
                    <p class="text-2xl font-bold text-white">{{ $stats['total_payments'] }}</p>
                </div>
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                    <h3 class="text-sm text-gray-400 mb-1">Completed</h3>
                    <p class="text-2xl font-bold text-green-400">{{ $stats['completed_payments'] }}</p>
                </div>
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                    <h3 class="text-sm text-gray-400 mb-1">Pending</h3>
                    <p class="text-2xl font-bold text-yellow-400">{{ $stats['pending_payments'] }}</p>
                </div>
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                    <h3 class="text-sm text-gray-400 mb-1">Total Enrollments</h3>
                    <p class="text-2xl font-bold text-blue-400">{{ $stats['total_enrollments'] }}</p>
                </div>
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                    <h3 class="text-sm text-gray-400 mb-1">Active Enrollments</h3>
                    <p class="text-2xl font-bold text-purple-400">{{ $stats['active_enrollments'] }}</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Test Payment Creation -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h2 class="text-xl font-bold text-white mb-4">Test Payment Creation</h2>
                    <form id="test-payment-form" class="space-y-4">
                        @csrf
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Select Course</label>
                            <select name="course_id" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                                <option value="">Choose a course...</option>
                                @foreach($courses as $course)
                                    <option value="{{ $course->id }}">{{ $course->title }} - ${{ number_format($course->price, 2, '.', '') }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Select User</label>
                            <select name="user_id" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                                <option value="">Choose a user...</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors">
                            Create Test Payment
                        </button>
                    </form>
                    
                    <div id="payment-result" class="mt-4 hidden">
                        <div class="bg-gray-700 border border-gray-600 rounded-md p-4">
                            <h3 class="text-sm font-medium text-gray-300 mb-2">Result:</h3>
                            <pre id="payment-result-content" class="text-xs text-gray-400 whitespace-pre-wrap"></pre>
                        </div>
                    </div>
                </div>

                <!-- Test Payment Simulation -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h2 class="text-xl font-bold text-white mb-4">Simulate Payment Completion</h2>
                    <form id="simulate-payment-form" class="space-y-4">
                        @csrf
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Payment ID</label>
                            <input type="text" name="payment_id" placeholder="Enter payment ID from above test..." required 
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                        </div>
                        
                        <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors">
                            Simulate Payment Success
                        </button>
                    </form>
                    
                    <div id="simulation-result" class="mt-4 hidden">
                        <div class="bg-gray-700 border border-gray-600 rounded-md p-4">
                            <h3 class="text-sm font-medium text-gray-300 mb-2">Result:</h3>
                            <pre id="simulation-result-content" class="text-xs text-gray-400 whitespace-pre-wrap"></pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Check Enrollment Status -->
            <div class="mt-8 bg-gray-800 border border-gray-700 rounded-lg p-6">
                <h2 class="text-xl font-bold text-white mb-4">Check Enrollment Status</h2>
                <form id="check-enrollment-form" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    @csrf
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Select User</label>
                        <select name="user_id" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                            <option value="">Choose a user...</option>
                            @foreach($users as $user)
                                <option value="{{ $user->id }}">{{ $user->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Select Course</label>
                        <select name="course_id" required class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                            <option value="">Choose a course...</option>
                            @foreach($courses as $course)
                                <option value="{{ $course->id }}">{{ $course->title }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">
                            Check Status
                        </button>
                    </div>
                </form>
                
                <div id="enrollment-result" class="mt-4 hidden">
                    <div class="bg-gray-700 border border-gray-600 rounded-md p-4">
                        <h3 class="text-sm font-medium text-gray-300 mb-2">Enrollment Status:</h3>
                        <pre id="enrollment-result-content" class="text-xs text-gray-400 whitespace-pre-wrap"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
// Test Payment Creation
document.getElementById('test-payment-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const resultDiv = document.getElementById('payment-result');
    const resultContent = document.getElementById('payment-result-content');
    
    try {
        const response = await fetch('/test/payment/purchase', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const result = await response.json();
        resultContent.textContent = JSON.stringify(result, null, 2);
        resultDiv.classList.remove('hidden');
        
        // Auto-fill payment ID if successful
        if (result.success && result.payment_id) {
            document.querySelector('input[name="payment_id"]').value = result.payment_id;
        }
    } catch (error) {
        resultContent.textContent = 'Error: ' + error.message;
        resultDiv.classList.remove('hidden');
    }
});

// Simulate Payment Completion
document.getElementById('simulate-payment-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const resultDiv = document.getElementById('simulation-result');
    const resultContent = document.getElementById('simulation-result-content');
    
    try {
        const response = await fetch('/test/payment/simulate-success', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const result = await response.json();
        resultContent.textContent = JSON.stringify(result, null, 2);
        resultDiv.classList.remove('hidden');
    } catch (error) {
        resultContent.textContent = 'Error: ' + error.message;
        resultDiv.classList.remove('hidden');
    }
});

// Check Enrollment Status
document.getElementById('check-enrollment-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const resultDiv = document.getElementById('enrollment-result');
    const resultContent = document.getElementById('enrollment-result-content');
    
    try {
        const response = await fetch('/test/payment/check-enrollment', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const result = await response.json();
        resultContent.textContent = JSON.stringify(result, null, 2);
        resultDiv.classList.remove('hidden');
    } catch (error) {
        resultContent.textContent = 'Error: ' + error.message;
        resultDiv.classList.remove('hidden');
    }
});
</script>
@endpush
