@extends('layouts.app')

@section('title', 'Page Not Found - Escape Matrix Academy')
@section('description', 'The page you are looking for could not be found. Return to our courses or login to access your dashboard.')

@section('content')
<section class="min-h-screen flex items-center justify-center py-20 bg-gradient-to-br from-black via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Error Code -->
            <div class="mb-8">
                <h1 class="text-8xl md:text-9xl font-bold text-red-500 mb-4">404</h1>
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Page Not Found</h2>
                <p class="text-xl text-gray-400 mb-8">
                    The page you're looking for doesn't exist or has been moved.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                @auth
                    <!-- For authenticated users -->
                    <a href="{{ route('courses.index') }}" class="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-book mr-2"></i>
                        Browse Courses
                    </a>
                    <a href="{{ route('dashboard') }}" class="border border-gray-600 text-white hover:bg-gray-800 px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Dashboard
                    </a>
                @else
                    <!-- For unauthenticated users -->
                    <a href="{{ route('login') }}" class="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login
                    </a>
                    <a href="{{ route('courses.index') }}" class="border border-gray-600 text-white hover:bg-gray-800 px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-book mr-2"></i>
                        Browse Courses
                    </a>
                @endauth
            </div>

            <!-- Back to Home -->
            <div class="text-center">
                <a href="{{ route('home') }}" class="text-gray-400 hover:text-white transition-colors inline-flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </div>
</section>
@endsection