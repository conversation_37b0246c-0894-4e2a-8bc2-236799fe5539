<?php

namespace App\Console\Commands;

use App\Services\PayPalService;
use App\Exceptions\PayPalException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Exception;

class CheckPayPalStatus extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'paypal:check-status {--notify : Send notification if issues detected}';

    /**
     * The console command description.
     */
    protected $description = 'Check PayPal account status and payment system health';

    protected $paypalService;

    /**
     * Create a new command instance.
     */
    public function __construct(PayPalService $paypalService)
    {
        parent::__construct();
        $this->paypalService = $paypalService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking PayPal account status...');
        $this->newLine();
        
        $issues = [];
        
        // Test 1: Check API connectivity
        $this->info('1. Testing PayPal API connectivity...');
        try {
            $token = $this->paypalService->getAccessToken();
            if ($token) {
                $this->info('   ✅ API connectivity: OK');
            } else {
                $this->error('   ❌ API connectivity: Failed to get access token');
                $issues[] = 'API connectivity failed';
            }
        } catch (Exception $e) {
            $this->error('   ❌ API connectivity: ' . $e->getMessage());
            $issues[] = 'API connectivity error: ' . $e->getMessage();
        }
        
        // Test 2: Test order creation with minimal amount
        $this->info('2. Testing order creation...');
        try {
            $testOrderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [[
                    'reference_id' => 'test-' . uniqid(),
                    'amount' => [
                        'currency_code' => 'USD',
                        'value' => '0.01'
                    ],
                    'description' => 'PayPal Status Check Test Order',
                    'custom_id' => 'status-check-' . time(),
                    'invoice_id' => 'TEST-' . uniqid()
                ]],
                'application_context' => [
                    'brand_name' => config('app.name', 'Test'),
                    'locale' => 'en-US',
                    'landing_page' => 'BILLING',
                    'shipping_preference' => 'NO_SHIPPING',
                    'user_action' => 'PAY_NOW',
                    'return_url' => url('/test/success'),
                    'cancel_url' => url('/test/cancel')
                ]
            ];
            
            $result = $this->paypalService->createOrder($testOrderData);
            
            if (isset($result['id'])) {
                $this->info('   ✅ Order creation: OK (Order ID: ' . $result['id'] . ')');
                
                // Clean up test order if possible
                $this->info('   🧹 Cleaning up test order...');
                // Note: PayPal doesn't allow deleting orders, they expire automatically
            } else {
                $this->error('   ❌ Order creation: Invalid response format');
                $issues[] = 'Order creation returned invalid response';
            }
            
        } catch (PayPalException $e) {
            $errorCode = $e->getPayPalErrorCode();
            $this->error('   ❌ Order creation failed: ' . $errorCode);
            $this->error('      Message: ' . $e->getMessage());
            
            if ($errorCode === 'PAYEE_ACCOUNT_RESTRICTED') {
                $this->error('   🚨 CRITICAL: Your PayPal account is RESTRICTED!');
                $this->error('      This means ALL payments are currently failing.');
                $this->error('      Action required: Check PayPal Dashboard immediately.');
                $issues[] = 'CRITICAL: PayPal account restricted - all payments failing';
            } else {
                $issues[] = 'Order creation failed: ' . $errorCode . ' - ' . $e->getMessage();
            }
            
        } catch (Exception $e) {
            $this->error('   ❌ Order creation: ' . $e->getMessage());
            $issues[] = 'Order creation error: ' . $e->getMessage();
        }
        
        // Test 3: Check configuration
        $this->info('3. Checking PayPal configuration...');
        $config = [
            'client_id' => config('paypal.client_id') ? 'Set' : 'Missing',
            'client_secret' => config('paypal.client_secret') ? 'Set' : 'Missing',
            'mode' => config('paypal.mode', 'Not set'),
            'base_url' => config('paypal.base_url', 'Not set')
        ];
        
        foreach ($config as $key => $value) {
            if ($value === 'Missing' || $value === 'Not set') {
                $this->error('   ❌ ' . ucfirst(str_replace('_', ' ', $key)) . ': ' . $value);
                $issues[] = 'Configuration issue: ' . $key . ' is ' . strtolower($value);
            } else {
                $this->info('   ✅ ' . ucfirst(str_replace('_', ' ', $key)) . ': ' . $value);
            }
        }
        
        $this->newLine();
        
        // Summary
        if (empty($issues)) {
            $this->info('🎉 PayPal Status Check: ALL SYSTEMS OPERATIONAL');
            $this->info('Your PayPal integration is working correctly.');
            
            Log::info('PayPal status check completed successfully', [
                'timestamp' => now()->toISOString(),
                'status' => 'operational'
            ]);
            
            return 0;
        } else {
            $this->error('⚠️  PayPal Status Check: ISSUES DETECTED');
            $this->error('Found ' . count($issues) . ' issue(s):');
            
            foreach ($issues as $index => $issue) {
                $this->error('   ' . ($index + 1) . '. ' . $issue);
            }
            
            $this->newLine();
            $this->info('📋 Recommended Actions:');
            $this->info('1. Check PayPal Business Dashboard: https://www.paypal.com/businessmanage/');
            $this->info('2. Verify API credentials in your .env file');
            $this->info('3. Check for any account limitations or required actions');
            $this->info('4. Contact PayPal Business Support if issues persist');
            
            Log::warning('PayPal status check detected issues', [
                'timestamp' => now()->toISOString(),
                'issues' => $issues,
                'status' => 'issues_detected'
            ]);
            
            // Send notification if requested
            if ($this->option('notify') && config('app.admin_email')) {
                $this->info('📧 Sending notification to admin...');
                // You could implement a notification here
            }
            
            return 1;
        }
    }
}