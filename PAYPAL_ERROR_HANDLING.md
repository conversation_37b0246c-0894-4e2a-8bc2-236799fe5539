# PayPal Error Handling & Recovery System

This document outlines the enhanced PayPal error handling system implemented to address payment processing issues, particularly the `PAYEE_ACCOUNT_RESTRICTED` error.

## 🚨 Issue Overview

The `PAYEE_ACCOUNT_RESTRICTED` error occurs when PayPal restricts your merchant account, causing ALL payment processing to fail. This typically happens due to:

- Account compliance issues
- Verification requirements
- Policy violations
- Suspicious activity detection
- Business verification needs

## 🛠️ Enhanced Error Handling Features

### 1. Improved User Experience

**Enhanced Error Page** (`resources/views/payments/error.blade.php`)
- Different messaging for account restriction errors
- Shows debug ID for support reference
- Conditional retry button (hidden for non-retryable errors)
- Pre-filled support contact form
- Clear status updates

**Maintenance Mode Page** (`resources/views/maintenance/payment-system.blade.php`)
- Dedicated page for payment system issues
- Auto-refresh functionality
- Clear user guidance
- Course bookmarking suggestions

### 2. Admin Notification System

**Email Notifications** (`app/Notifications/PaymentIssueNotification.php`)
- Immediate email alerts for critical errors
- Detailed error information and context
- Priority-based notifications
- Actionable recommendations
- PayPal dashboard links

**Enhanced Logging** (`app/Http/Controllers/PayPalController.php`)
- Structured error logging
- Critical alerts for account restrictions
- Debug ID tracking
- User and course context

### 3. Monitoring & Diagnostics

**Status Check Command** (`app/Console/Commands/CheckPayPalStatus.php`)
```bash
# Check PayPal system status
php artisan paypal:check-status

# Check status and send notifications
php artisan paypal:check-status --notify
```

**Enhanced Exception Handling** (`app/Exceptions/PayPalException.php`)
- Error categorization (retryable vs non-retryable)
- Support requirement flags
- User-friendly error messages
- Retry delay recommendations

## 🔧 Configuration

### Environment Variables

Add to your `.env` file:
```env
# Admin email for critical payment alerts
APP_ADMIN_EMAIL=<EMAIL>

# PayPal configuration (existing)
PAYPAL_CLIENT_ID=your_client_id
PAYPAL_CLIENT_SECRET=your_client_secret
PAYPAL_MODE=live  # or sandbox
```

### Mail Configuration

Ensure your mail configuration is set up for notifications:
```env
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

## 🚀 Usage

### For Administrators

1. **Monitor System Health**
   ```bash
   # Run daily health checks
   php artisan paypal:check-status
   ```

2. **Set Up Cron Job** (recommended)
   ```bash
   # Add to crontab for automated monitoring
   0 */6 * * * cd /path/to/your/app && php artisan paypal:check-status --notify
   ```

3. **Emergency Response**
   - Check email for critical alerts
   - Log into PayPal Business Dashboard
   - Review account limitations
   - Contact PayPal Business Support if needed

### For Users

1. **Enhanced Error Experience**
   - Clear error messages
   - Debug ID for support reference
   - Alternative actions (browse courses, contact support)
   - No retry button for non-retryable errors

2. **Maintenance Mode**
   - Automatic page refresh
   - Clear status updates
   - Course bookmarking options

## 📊 Error Types & Responses

| Error Code | Severity | User Message | Admin Action |
|------------|----------|--------------|-------------|
| `PAYEE_ACCOUNT_RESTRICTED` | Critical | "Payment system temporarily unavailable" | Immediate PayPal dashboard check |
| `PAYEE_ACCOUNT_LOCKED_OR_CLOSED` | Critical | "Payment cannot be processed" | Contact PayPal support |
| `INSTRUMENT_DECLINED` | Medium | "Payment method declined" | Monitor for patterns |
| `INSUFFICIENT_FUNDS` | Low | "Insufficient funds" | User action required |

## 🔍 Troubleshooting

### Common Issues

1. **Account Restricted Error**
   - Check PayPal Business Dashboard
   - Look for account limitations
   - Complete any required verifications
   - Contact PayPal Business Support

2. **API Connectivity Issues**
   - Verify API credentials
   - Check network connectivity
   - Test with sandbox environment
   - Review PayPal API status

3. **Email Notifications Not Working**
   - Check mail configuration
   - Verify `APP_ADMIN_EMAIL` setting
   - Test mail functionality
   - Check spam folders

### Debug Commands

```bash
# Test PayPal connectivity
php artisan paypal:check-status

# Check application logs
tail -f storage/logs/laravel.log

# Test email configuration
php artisan tinker
>>> Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });
```

## 📈 Monitoring Recommendations

1. **Set up automated health checks** every 6 hours
2. **Monitor application logs** for PayPal errors
3. **Set up email alerts** for critical issues
4. **Regular PayPal dashboard reviews**
5. **Keep backup payment methods** ready (if applicable)

## 🆘 Emergency Contacts

- **PayPal Business Support**: [PayPal Help Center](https://www.paypal.com/us/smarthelp/contact-us)
- **PayPal Developer Support**: [Developer Community](https://developer.paypal.com/support/)
- **Application Support**: Contact your development team

## 📝 Logs & Debugging

### Key Log Locations
- Application logs: `storage/logs/laravel.log`
- PayPal errors: Search for "PayPal" in logs
- Critical alerts: Search for "PAYEE_ACCOUNT_RESTRICTED"

### Debug Information
- Debug ID: Provided in error responses
- Correlation ID: PayPal request tracking
- Timestamp: Error occurrence time
- User context: User ID and course information

---

**Last Updated**: January 2025
**Version**: 1.0
**Maintainer**: Development Team