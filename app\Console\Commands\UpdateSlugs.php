<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;

class UpdateSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'slugs:update {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update slugs for courses, chapters, and lectures to ensure they are current with titles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        } else {
            $this->info('🔄 Updating slugs for courses, chapters, and lectures...');
        }
        
        $this->line('');

        // Update course slugs
        $this->updateCoursesSlugs($dryRun);
        
        // Update chapter slugs
        $this->updateChapterSlugs($dryRun);
        
        // Update lecture slugs
        $this->updateLectureSlugs($dryRun);

        $this->line('');
        if ($dryRun) {
            $this->info('✅ Dry run completed. Use --no-dry-run to apply changes.');
        } else {
            $this->info('✅ Slug updates completed successfully!');
        }
    }

    /**
     * Update course slugs
     */
    private function updateCoursesSlugs(bool $dryRun): void
    {
        $this->info('📚 Processing courses...');
        
        $courses = Course::all();
        $updated = 0;
        
        foreach ($courses as $course) {
            $newSlug = $course->generateUniqueSlug($course->title, $course->id);
            
            if ($course->slug !== $newSlug) {
                if ($dryRun) {
                    $this->line("   Would update course '{$course->title}': {$course->slug} → {$newSlug}");
                } else {
                    $course->update(['slug' => $newSlug]);
                    $this->line("   Updated course '{$course->title}': {$course->slug} → {$newSlug}");
                }
                $updated++;
            }
        }
        
        if ($updated === 0) {
            $this->line('   No course slugs need updating');
        } else {
            $this->line("   {$updated} course(s) " . ($dryRun ? 'would be' : '') . " updated");
        }
    }

    /**
     * Update chapter slugs
     */
    private function updateChapterSlugs(bool $dryRun): void
    {
        $this->info('📖 Processing chapters...');
        
        $chapters = Chapter::all();
        $updated = 0;
        
        foreach ($chapters as $chapter) {
            $newSlug = $chapter->generateUniqueSlug($chapter->title, $chapter->course_id, $chapter->id);
            
            if ($chapter->slug !== $newSlug) {
                if ($dryRun) {
                    $this->line("   Would update chapter '{$chapter->title}': {$chapter->slug} → {$newSlug}");
                } else {
                    $chapter->update(['slug' => $newSlug]);
                    $this->line("   Updated chapter '{$chapter->title}': {$chapter->slug} → {$newSlug}");
                }
                $updated++;
            }
        }
        
        if ($updated === 0) {
            $this->line('   No chapter slugs need updating');
        } else {
            $this->line("   {$updated} chapter(s) " . ($dryRun ? 'would be' : '') . " updated");
        }
    }

    /**
     * Update lecture slugs
     */
    private function updateLectureSlugs(bool $dryRun): void
    {
        $this->info('🎥 Processing lectures...');
        
        $lectures = Lecture::all();
        $updated = 0;
        
        foreach ($lectures as $lecture) {
            $newSlug = $lecture->generateUniqueLectureSlug($lecture->title, $lecture->chapter_id, $lecture->id);
            
            if ($lecture->slug !== $newSlug) {
                if ($dryRun) {
                    $this->line("   Would update lecture '{$lecture->title}': {$lecture->slug} → {$newSlug}");
                } else {
                    $lecture->update(['slug' => $newSlug]);
                    $this->line("   Updated lecture '{$lecture->title}': {$lecture->slug} → {$newSlug}");
                }
                $updated++;
            }
        }
        
        if ($updated === 0) {
            $this->line('   No lecture slugs need updating');
        } else {
            $this->line("   {$updated} lecture(s) " . ($dryRun ? 'would be' : '') . " updated");
        }
    }
}
