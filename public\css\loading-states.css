/* Modern Loading States for Buttons and Interactive Elements */

/* Button Loading States */
.btn-loading {
    position: relative;
    pointer-events: none;
    overflow: hidden;
}

.btn-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

.btn-loading .btn-text {
    opacity: 0.7;
}

.btn-loading .btn-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

/* Enhanced Complete Button States */
.complete-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.complete-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.complete-btn.loading {
    background: linear-gradient(45deg, #6366f1, #8b5cf6);
    color: white;
    cursor: not-allowed;
}

.complete-btn.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 1.2s infinite;
}

.complete-btn.success {
    background: linear-gradient(45deg, #10b981, #059669);
    color: white;
    transform: scale(1.05);
}

.complete-btn.success::before {
    content: '✓';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    animation: checkmark 0.5s ease-in-out;
}

/* Navigation Button Loading */
.nav-btn.loading {
    background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
    color: #6b7280;
    cursor: not-allowed;
}

.nav-btn.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #d1d5db;
    border-top: 2px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Form Loading States */
.form-loading {
    position: relative;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.form-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

/* Sidebar Loading */
.sidebar-loading {
    opacity: 0.6;
    pointer-events: none;
}

.sidebar-loading .sidebar-item {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
    height: 20px;
    margin-bottom: 8px;
}

/* Animations */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes checkmark {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn-loading .btn-spinner {
        width: 14px;
        height: 14px;
        margin-right: 6px;
    }
    
    .complete-btn {
        padding: 8px 16px;
        font-size: 14px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .form-loading::after {
        background: rgba(17, 24, 39, 0.8);
    }
    
    .sidebar-loading .sidebar-item {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-loading::before {
        background: linear-gradient(
            90deg,
            transparent,
            rgba(0, 0, 0, 0.3),
            transparent
        );
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .btn-loading::before,
    .complete-btn.loading::after,
    .sidebar-loading .sidebar-item,
    .btn-spinner,
    .nav-btn.loading::before,
    .form-loading::before {
        animation: none;
    }
    
    .complete-btn:hover {
        transform: none;
    }
}