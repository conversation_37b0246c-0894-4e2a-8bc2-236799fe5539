@extends('layouts.app')

@section('title', 'User Management - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">User Management</h1>
                <p class="text-gray-400 mt-2">Manage system users and their roles</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('admin.users.export') }}" class="btn-secondary">
                    <i class="fas fa-download mr-2"></i>Export Users
                </a>
                <a href="{{ route('admin.users.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Add User
                </a>
                <a href="{{ route('admin.dashboard') }}" class="btn-secondary">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <i class="fas fa-users text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Total Users</p>
                    <p class="text-white text-2xl font-bold">{{ $totalUsers ?? 0 }}</p>
                </div>
            </div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <i class="fas fa-user-check text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Active Users</p>
                    <p class="text-white text-2xl font-bold">{{ $activeUsers ?? 0 }}</p>
                </div>
            </div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <i class="fas fa-user-graduate text-purple-400 text-xl"></i>
                </div>
                <!-- <div class="ml-4">
                    <p class="text-gray-400 text-sm">Students</p>
                    <p class="text-white text-2xl font-bold">{{ $students ?? 0 }}</p>
                </div> -->
            </div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <i class="fas fa-chalkboard-teacher text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Instructors</p>
                    <p class="text-white text-2xl font-bold">{{ $instructors ?? 0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="dark-card rounded-lg shadow-lg p-6 mb-6">
        <form method="GET" action="{{ route('admin.users.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Search</label>
                <input type="text" name="search" value="{{ request('search') }}" placeholder="Name or email..." class="dark-input">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Role</label>
                <select name="role" class="dark-input">
                    <option value="">All Roles</option>
                    <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                    <option value="instructor" {{ request('role') === 'instructor' ? 'selected' : '' }}>Instructor</option>
                    <option value="student" {{ request('role') === 'student' ? 'selected' : '' }}>Student</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                <select name="status" class="dark-input">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <div class="flex items-end space-x-2">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Users Table -->
    <div class="dark-card rounded-lg shadow-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-700">
                <thead class="bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Joined</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-gray-900 divide-y divide-gray-700">
                    @forelse($users ?? [] as $user)
                        <tr class="hover:bg-gray-800 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">{{ substr($user->name ?? 'U', 0, 1) }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-white">{{ $user->name ?? 'N/A' }}</div>
                                        <div class="text-sm text-gray-400">{{ $user->email ?? 'N/A' }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if(isset($user->roles))
                                    @foreach($user->roles as $role)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-200 mr-1">
                                            {{ $role->name }}
                                        </span>
                                    @endforeach
                                @else
                                    <span class="text-gray-400">No roles</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ ($user->status ?? 'active') === 'active' ? 'bg-green-900 text-green-200' : 'bg-red-900 text-red-200' }}">
                                    {{ ucfirst($user->status ?? 'active') }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                {{ isset($user->created_at) ? $user->created_at->format('M d, Y') : 'N/A' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.users.show', $user->id ?? 1) }}" class="text-blue-400 hover:text-blue-300">View</a>
                                    <a href="{{ route('admin.users.edit', $user->id ?? 1) }}" class="text-yellow-400 hover:text-yellow-300">Edit</a>
                                    @if(($user->id ?? 0) !== auth()->id())
                                        <form method="POST" action="{{ route('admin.users.destroy', $user->id ?? 1) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-400 hover:text-red-300">Delete</button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-400">
                                No users found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if(isset($users) && $users->hasPages())
        <div class="dark-card px-4 py-3 flex items-center justify-between border-t border-gray-700 sm:px-6 mt-6">
            <div class="flex-1 flex justify-between sm:hidden">
                @if($users->onFirstPage())
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-400 bg-gray-800 cursor-default">
                        Previous
                    </span>
                @else
                    <a href="{{ $users->previousPageUrl() }}" class="relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700">
                        Previous
                    </a>
                @endif

                @if($users->hasMorePages())
                    <a href="{{ $users->nextPageUrl() }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700">
                        Next
                    </a>
                @else
                    <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-400 bg-gray-800 cursor-default">
                        Next
                    </span>
                @endif
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-300">
                        Showing
                        <span class="font-medium text-white">{{ $users->firstItem() }}</span>
                        to
                        <span class="font-medium text-white">{{ $users->lastItem() }}</span>
                        of
                        <span class="font-medium text-white">{{ $users->total() }}</span>
                        results
                    </p>
                </div>
                <div>
                    {{ $users->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    @endif
</div>
@endsection