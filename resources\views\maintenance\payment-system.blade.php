@extends('layouts.app')

@section('title', 'Payment System Maintenance')

@section('content')
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <div class="mx-auto h-24 w-24 text-yellow-500">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Payment System Maintenance
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                We're temporarily experiencing issues with our payment system
            </p>
        </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div class="space-y-6">
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Temporary Service Interruption
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>Our payment processing system is currently undergoing maintenance. We're working to resolve this as quickly as possible.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">
                                What You Can Do
                            </h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Bookmark this course to purchase later</li>
                                    <li>Check back in a few hours</li>
                                    <li>Contact our support team for updates</li>
                                    <li>Follow us on social media for real-time updates</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                @if(isset($course))
                <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Course Details</h4>
                    <div class="text-sm text-gray-600">
                        <p><strong>Course:</strong> {{ $course->title }}</p>
                        <p><strong>Price:</strong> ${{ number_format($course->price, 2, '.', '') }}</p>
                        <p><strong>Your Interest:</strong> We've noted your interest in this course</p>
                    </div>
                </div>
                @endif

                <div class="flex flex-col space-y-3">
                    <a href="{{ route('courses.index') }}" 
                       class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        Browse Other Courses
                    </a>
                    
                    <a href="{{ route('contact') }}" 
                       class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        Contact Support
                    </a>
                    
                    <button onclick="window.location.reload()" 
                            class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh Page
                    </button>
                </div>

                <div class="text-center">
                    <p class="text-xs text-gray-500">
                        Status: <span class="font-medium">Under Maintenance</span><br>
                        Last Updated: {{ now()->format('M j, Y \\a\\t g:i A T') }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Optional: Auto-refresh every 5 minutes -->
    <script>
        // Auto-refresh page every 5 minutes to check if maintenance is over
        setTimeout(function() {
            window.location.reload();
        }, 300000); // 5 minutes
        
        // Show countdown
        let countdown = 300; // 5 minutes in seconds
        const countdownElement = document.createElement('div');
        countdownElement.className = 'text-center mt-4 text-xs text-gray-400';
        document.querySelector('.min-h-screen').appendChild(countdownElement);
        
        const updateCountdown = () => {
            const minutes = Math.floor(countdown / 60);
            const seconds = countdown % 60;
            countdownElement.textContent = `Auto-refresh in ${minutes}:${seconds.toString().padStart(2, '0')}`;
            countdown--;
            
            if (countdown < 0) {
                countdownElement.textContent = 'Refreshing...';
            }
        };
        
        updateCountdown();
        setInterval(updateCountdown, 1000);
    </script>
</div>
@endsection