@extends('layouts.app')

@section('title', 'Service Unavailable - Escape Matrix Academy')
@section('description', 'We are currently performing maintenance to improve your experience. Please check back shortly.')

@section('content')
<section class="min-h-screen flex items-center justify-center py-20 bg-gradient-to-br from-black via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Error Code -->
            <div class="mb-8">
                <h1 class="text-8xl md:text-9xl font-bold text-red-500 mb-4">503</h1>
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Service Unavailable</h2>
                <p class="text-xl text-gray-400 mb-8">
                    We're currently performing maintenance to improve your experience. Please check back shortly.
                </p>
            </div>

            <!-- Maintenance Icon -->
            <div class="mb-8">
                <div class="inline-flex items-center justify-center w-24 h-24 bg-gray-800 rounded-full mb-4">
                    <i class="fas fa-tools text-3xl text-red-500"></i>
                </div>
                <p class="text-gray-400">
                    Expected downtime: A few minutes
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button onclick="window.location.reload()" class="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Try Again
                </button>
                
                @auth
                    <!-- For authenticated users -->
                    <a href="{{ route('courses.index') }}" class="border border-gray-600 text-white hover:bg-gray-800 px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-book mr-2"></i>
                        Browse Courses
                    </a>
                @else
                    <!-- For unauthenticated users -->
                    <a href="{{ route('login') }}" class="border border-gray-600 text-white hover:bg-gray-800 px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login
                    </a>
                @endauth
            </div>

            <!-- Back to Home -->
            <div class="text-center">
                <a href="{{ route('home') }}" class="text-gray-400 hover:text-white transition-colors inline-flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </div>
</section>
@endsection