<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type' => 'general',
    'modules' => null,
    'courseId' => null,
    'enrollmentId' => null,
    'courseSlug' => null,
    'requiresAuth' => null,
    'requiresInstructor' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type' => 'general',
    'modules' => null,
    'courseId' => null,
    'enrollmentId' => null,
    'courseSlug' => null,
    'requiresAuth' => null,
    'requiresInstructor' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>


<meta name="page-type" content="<?php echo e($type); ?>">


<?php $__env->startPush('body-attributes'); ?>
    data-page-type="<?php echo e($type); ?>"
    <?php if($courseId): ?> data-course-id="<?php echo e($courseId); ?>" <?php endif; ?>
    <?php if($enrollmentId): ?> data-enrollment-id="<?php echo e($enrollmentId); ?>" <?php endif; ?>
    <?php if($courseSlug): ?> data-course-slug="<?php echo e($courseSlug); ?>" <?php endif; ?>
    <?php if($requiresAuth !== null): ?> data-requires-auth="<?php echo e($requiresAuth ? 'true' : 'false'); ?>" <?php endif; ?>
    <?php if($requiresInstructor !== null): ?> data-requires-instructor="<?php echo e($requiresInstructor ? 'true' : 'false'); ?>" <?php endif; ?>
<?php $__env->stopPush(); ?>


<?php if($modules): ?>
    <script>
        window.pageModules = <?php echo json_encode(is_string($modules) ? explode(', ', $modules) : $modules) ?>;
    </script>
<?php endif; ?>


<?php $__env->startPush('body-classes'); ?>
    page-<?php echo e(str_replace('_', '-', $type)); ?>

    <?php if($requiresAuth): ?> requires-auth <?php endif; ?>
    <?php if($requiresInstructor): ?> requires-instructor <?php endif; ?>
<?php $__env->stopPush(); ?>


<?php if($type === 'course-builder'): ?>
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js" as="script">
    <link rel="preload" href="<?php echo e(asset('js/instructor/course-builder/course-builder-main.js')); ?>" as="script">
<?php elseif($type === 'course-viewer'): ?>
    <link rel="preload" href="<?php echo e(asset('js/course-viewer.js')); ?>" as="script">
<?php elseif($type === 'auth'): ?>
    <link rel="preload" href="<?php echo e(asset('js/auth-forms.js')); ?>" as="script">
<?php elseif($type === 'payments'): ?>
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/chart.js" as="script">
<?php endif; ?>


<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "<?php echo e($type); ?> Page",
    "description": "EscapeMatrix <?php echo e(ucfirst(str_replace(['_', '-'], ' ', $type))); ?> Page",
    "url": "<?php echo e(url()->current()); ?>",
    "isPartOf": {
        "@type": "WebSite",
        "name": "EscapeMatrix",
        "url": "<?php echo e(url('/')); ?>"
    }
    <?php if($courseId): ?>
    ,"about": {
        "@type": "Course",
        "identifier": "<?php echo e($courseId); ?>"
        <?php if($courseSlug): ?>
        ,"url": "<?php echo e(url('/courses/' . $courseSlug)); ?>"
        <?php endif; ?>
    }
    <?php endif; ?>
}
</script><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/components/page-setup.blade.php ENDPATH**/ ?>