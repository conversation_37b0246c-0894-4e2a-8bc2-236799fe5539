<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Chapter extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'learning_objectives',
        'course_id',
        'instructor_id',
        'sort_order',
        'is_published',
        'is_free_preview',
        'total_duration_minutes',
        'total_lectures',
    ];

    protected $casts = [
        'learning_objectives' => 'array',
        'sort_order' => 'integer',
        'is_published' => 'boolean',
        'is_free_preview' => 'boolean',
        'total_duration_minutes' => 'integer',
        'total_lectures' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
            if (empty($model->slug)) {
                $model->slug = $model->generateUniqueSlug($model->title, $model->course_id);
            }
            if (is_null($model->sort_order)) {
                $model->sort_order = static::where('course_id', $model->course_id)->max('sort_order') + 1;
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('title')) {
                $model->slug = $model->generateUniqueSlug($model->title, $model->course_id, $model->id);
            }
        });

        static::saved(function ($model) {
            // Update course statistics when chapter is saved
            $model->course->updateStatistics();
        });

        static::deleted(function ($model) {
            // Update course statistics when chapter is deleted
            $model->course->updateStatistics();
        });
    }

    /**
     * Get the course that owns the chapter.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the instructor that owns the chapter.
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    /**
     * Get the lectures for the chapter.
     */
    public function lectures(): HasMany
    {
        return $this->hasMany(Lecture::class)->orderBy('sort_order');
    }

    /**
     * Get the published lectures for the chapter.
     */
    public function publishedLectures(): HasMany
    {
        return $this->lectures()->where('is_published', true);
    }

    /**
     * Get the free preview lectures for the chapter.
     */
    public function freePreviewLectures(): HasMany
    {
        return $this->lectures()->where('is_free_preview', true);
    }

    /**
     * Scope to filter published chapters.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to filter by course.
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * Scope to filter by instructor.
     */
    public function scopeByInstructor($query, $instructorId)
    {
        return $query->where('instructor_id', $instructorId);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if the chapter is published.
     */
    public function isPublished(): bool
    {
        return $this->is_published;
    }

    /**
     * Check if the chapter has free preview content.
     */
    public function hasFreePreview(): bool
    {
        return $this->is_free_preview || $this->freePreviewLectures()->exists();
    }

    /**
     * Get the chapter duration in human readable format.
     */
    public function getFormattedDuration(): string
    {
        if ($this->total_duration_minutes < 60) {
            return $this->total_duration_minutes . ' minutes';
        }
        
        $hours = floor($this->total_duration_minutes / 60);
        $minutes = $this->total_duration_minutes % 60;
        
        if ($minutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }
        
        return $hours . 'h ' . $minutes . 'm';
    }

    /**
     * Update chapter statistics.
     */
    public function updateStatistics(): void
    {
        $this->update([
            'total_lectures' => $this->lectures()->count(),
            'total_duration_minutes' => $this->lectures()->sum('duration_minutes'),
        ]);
    }

    /**
     * Get the next chapter in the course.
     */
    public function getNextChapter(): ?self
    {
        return static::where('course_id', $this->course_id)
            ->where('sort_order', '>', $this->sort_order)
            ->orderBy('sort_order')
            ->first();
    }

    /**
     * Get the previous chapter in the course.
     */
    public function getPreviousChapter(): ?self
    {
        return static::where('course_id', $this->course_id)
            ->where('sort_order', '<', $this->sort_order)
            ->orderBy('sort_order', 'desc')
            ->first();
    }

    /**
     * Move chapter up in sort order.
     */
    public function moveUp(): bool
    {
        $previousChapter = $this->getPreviousChapter();
        if (!$previousChapter) {
            return false;
        }

        $tempOrder = $this->sort_order;
        $this->sort_order = $previousChapter->sort_order;
        $previousChapter->sort_order = $tempOrder;

        $this->save();
        $previousChapter->save();

        return true;
    }

    /**
     * Move chapter down in sort order.
     */
    public function moveDown(): bool
    {
        $nextChapter = $this->getNextChapter();
        if (!$nextChapter) {
            return false;
        }

        $tempOrder = $this->sort_order;
        $this->sort_order = $nextChapter->sort_order;
        $nextChapter->sort_order = $tempOrder;

        $this->save();
        $nextChapter->save();

        return true;
    }

    /**
     * Generate a unique slug for the chapter within the course.
     */
    public function generateUniqueSlug(string $title, string $courseId, ?string $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = static::where('course_id', $courseId)->where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
