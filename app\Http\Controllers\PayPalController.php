<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Payment;
use App\Services\PayPalService;
use App\Exceptions\PayPalException;
use App\Notifications\PaymentIssueNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Exception;

class PayPalController extends Controller
{
    private PayPalService $paypalService;

    public function __construct(PayPalService $paypalService)
    {
        $this->paypalService = $paypalService;
    }

    /**
     * Initiate PayPal payment for course purchase
     */
    public function createPayment(Request $request, Course $course)
    {
        try {
            $user = Auth::user();

            // Check if user is already enrolled
            if ($user->enrollments()->where('course_id', $course->id)->exists()) {
                return redirect()->route('courses.show', $course)
                    ->with('error', 'You are already enrolled in this course.');
            }

            // Check if course is published
            if ($course->status !== 'published') {
                return redirect()->route('courses.index')
                    ->with('error', 'This course is not available for purchase.');
            }

            // Create PayPal order
            $result = $this->paypalService->createOrder($course, $user);

            if (!$result['success']) {
                Log::error('PayPal order creation failed', [
                    'user_id' => $user->id,
                    'course_id' => $course->id,
                    'error' => $result['error']
                ]);

                return redirect()->route('courses.show', $course)
                    ->with('error', 'Unable to process payment. Please try again.');
            }

            // Redirect to PayPal for approval
            return redirect($result['approval_url']);

        } catch (PayPalException $e) {
            Log::error('PayPal order creation failed', [
                'course_id' => $course->id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'paypal_error' => $e->getPayPalErrorCode(),
                'debug_id' => $e->getPayPalDebugId(),
                'requires_support' => $e->requiresSupport(),
                'is_retryable' => $e->isRetryable()
            ]);
            
            // Special handling for PAYEE_ACCOUNT_RESTRICTED
            if ($e->getPayPalErrorCode() === 'PAYEE_ACCOUNT_RESTRICTED') {
                // Send immediate notification to admin
                $this->notifyAdminOfPaymentIssue($e, $course);
                
                return redirect()->route('payments.error')
                    ->with('error', $e->getUserFriendlyMessage())
                    ->with('error_type', 'account_restricted')
                    ->with('support_required', true)
                    ->with('debug_id', $e->getPayPalDebugId());
            }
            
            return redirect()->route('payments.error')
                ->with('error', $e->getUserFriendlyMessage())
                ->with('is_retryable', $e->isRetryable())
                ->with('requires_support', $e->requiresSupport());
        } catch (Exception $e) {
            Log::error('PayPal payment initiation failed', [
                'user_id' => Auth::id(),
                'course_id' => $course->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('courses.show', $course)
                ->with('error', 'Payment processing error. Please try again.');
        }
    }

    /**
     * Handle successful PayPal payment
     */
    public function success(Request $request)
    {
        try {
            $orderId = $request->get('token');
            $payerId = $request->get('PayerID');

            if (!$orderId) {
                Log::warning('PayPal success callback missing token', [
                    'payer_id' => $payerId,
                    'request_data' => $request->all()
                ]);

                return view('payments.error', [
                    'error' => 'Payment verification failed - missing payment token.'
                ]);
            }

            // Capture the payment
            $result = $this->paypalService->captureOrder($orderId);

            if (!$result['success']) {
                Log::error('PayPal payment capture failed', [
                    'order_id' => $orderId,
                    'payer_id' => $payerId,
                    'error' => $result['error'] ?? 'Unknown error',
                    'result_data' => $result
                ]);

                return view('payments.error', [
                    'error' => $result['error'] ?? 'Payment capture failed. Please try again or contact support.'
                ]);
            }

            $payment = $result['payment'];

            // Create enrollment after successful payment
            DB::transaction(function () use ($payment) {
                Enrollment::firstOrCreate([
                    'user_id' => $payment->user_id,
                    'course_id' => $payment->course_id,
                ], [
                    'instructor_id' => $payment->instructor_id,
                    'enrolled_at' => now(),
                    'status' => 'active',
                    'progress_percentage' => 0,
                    'completed_lectures' => 0,
                    'total_lectures' => $payment->course->total_lectures ?? 0,
                    'total_watch_time_minutes' => 0,
                    'last_accessed_at' => now()
                ]);
            });

            Log::info('PayPal payment completed and enrollment created', [
                'payment_id' => $payment->id,
                'user_id' => $payment->user_id,
                'course_id' => $payment->course_id,
                'paypal_order_id' => $orderId
            ]);

            return view('payments.success', [
                'payment' => $payment,
                'course' => $payment->course
            ]);

        } catch (PayPalException $e) {
            Log::error('PayPal capture failed', [
                'order_id' => $orderId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'paypal_error' => $e->getPayPalErrorCode(),
                'debug_id' => $e->getPayPalDebugId(),
                'requires_support' => $e->requiresSupport(),
                'is_retryable' => $e->isRetryable()
            ]);
            
            return redirect()->route('payments.error')
                ->with('error', $e->getUserFriendlyMessage())
                ->with('is_retryable', $e->isRetryable())
                ->with('requires_support', $e->requiresSupport())
                ->with('debug_id', $e->getPayPalDebugId());
        } catch (Exception $e) {
            Log::error('Unexpected error during payment success handling', [
                'order_id' => $orderId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('payments.error')
                ->with('error', 'An unexpected error occurred while processing your payment. Please contact support.')
                ->with('requires_support', true);
        }
    }

    /**
     * Handle cancelled PayPal payment
     */
    public function cancel(Request $request)
    {
        $orderId = $request->get('token');

        if ($orderId) {
            // Mark payment as cancelled
            $payment = Payment::where('payment_id', $orderId)->first();
            if ($payment) {
                $payment->update(['status' => Payment::STATUS_CANCELLED]);

                Log::info('PayPal payment cancelled', [
                    'payment_id' => $payment->id,
                    'order_id' => $orderId
                ]);

                return view('payments.error', [
                    'course' => $payment->course,
                    'error' => 'Payment was cancelled. You can try again anytime.'
                ]);
            }
        }

        return view('payments.error', [
            'error' => 'Payment was cancelled.'
        ]);
    }

    /**
     * Handle PayPal webhooks
     */
    public function webhook(Request $request)
    {
        $startTime = microtime(true);
        $webhookId = uniqid('webhook_', true);

        try {
            $headers = $request->headers->all();
            $body = $request->getContent();
            $eventData = json_decode($body, true);

            // Log webhook received
            Log::info('PayPal webhook received', [
                'webhook_id' => $webhookId,
                'event_type' => $eventData['event_type'] ?? 'unknown',
                'resource_id' => $eventData['resource']['id'] ?? null,
                'transmission_id' => $headers['paypal-transmission-id'][0] ?? null
            ]);

            // Validate JSON structure
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('PayPal webhook invalid JSON', [
                    'webhook_id' => $webhookId,
                    'json_error' => json_last_error_msg(),
                    'body_preview' => substr($body, 0, 200)
                ]);
                return response()->json(['error' => 'Invalid JSON'], 400);
            }

            // Verify required webhook fields
            if (!isset($eventData['event_type']) || !isset($eventData['resource'])) {
                Log::error('PayPal webhook missing required fields', [
                    'webhook_id' => $webhookId,
                    'event_data_keys' => array_keys($eventData)
                ]);
                return response()->json(['error' => 'Missing required fields'], 400);
            }

            // Verify webhook signature
            if (!$this->paypalService->verifyWebhookSignature($headers, $body)) {
                Log::warning('PayPal webhook signature verification failed', [
                    'webhook_id' => $webhookId,
                    'transmission_id' => $headers['paypal-transmission-id'][0] ?? null
                ]);
                return response()->json(['error' => 'Invalid signature'], 401);
            }

            // Check for duplicate webhook (idempotency)
            $transmissionId = $headers['paypal-transmission-id'][0] ?? null;
            if ($transmissionId && $this->isDuplicateWebhook($transmissionId)) {
                Log::info('PayPal webhook duplicate detected', [
                    'webhook_id' => $webhookId,
                    'transmission_id' => $transmissionId
                ]);
                return response()->json(['status' => 'duplicate_processed'], 200);
            }

            // Process the webhook event
            $result = $this->paypalService->processWebhookEvent($eventData);

            // Store webhook processing result
            if ($transmissionId) {
                $this->storeWebhookResult($transmissionId, $result);
            }

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            if ($result['success']) {
                Log::info('PayPal webhook processed successfully', [
                    'webhook_id' => $webhookId,
                    'processing_time_ms' => $processingTime,
                    'event_type' => $eventData['event_type']
                ]);
                return response()->json(['status' => 'success']);
            } else {
                Log::error('PayPal webhook processing failed', [
                    'webhook_id' => $webhookId,
                    'processing_time_ms' => $processingTime,
                    'error' => $result['error']
                ]);
                return response()->json(['error' => $result['error']], 400);
            }

        } catch (Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::error('PayPal webhook processing exception', [
                'webhook_id' => $webhookId,
                'processing_time_ms' => $processingTime,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'body_preview' => substr($request->getContent(), 0, 500)
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Get payment status (AJAX endpoint)
     */
    public function getPaymentStatus(Request $request)
    {
        try {
            $orderId = $request->get('order_id');
            
            if (!$orderId) {
                return response()->json(['error' => 'Order ID required'], 400);
            }

            $payment = Payment::where('payment_id', $orderId)->first();
            
            if (!$payment) {
                return response()->json(['error' => 'Payment not found'], 404);
            }

            // Check if user owns this payment
            if ($payment->user_id !== Auth::id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            return response()->json([
                'status' => $payment->status,
                'amount' => $payment->formatted_amount,
                'course' => $payment->course->title,
                'created_at' => $payment->created_at->format('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            Log::error('Payment status check failed', [
                'error' => $e->getMessage(),
                'order_id' => $request->get('order_id')
            ]);

            return response()->json(['error' => 'Status check failed'], 500);
        }
    }

    /**
     * Show payment history for authenticated user
     */
    public function paymentHistory(Request $request)
    {
        try {
            $user = Auth::user();
            
            $payments = Payment::where('user_id', $user->id)
                ->with(['course:id,title,slug,instructor_id', 'course.instructor:id,name'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            return view('payments.history', compact('payments'));

        } catch (Exception $e) {
            Log::error('Payment history retrieval failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->route('dashboard')
                ->with('error', 'Unable to load payment history.');
        }
    }

    /**
     * Show payment details
     */
    public function paymentDetails(Payment $payment)
    {
        try {
            // Check if user owns this payment
            if ($payment->user_id !== Auth::id()) {
                abort(403, 'Unauthorized access to payment details.');
            }

            return view('payments.details', compact('payment'));

        } catch (Exception $e) {
            Log::error('Payment details retrieval failed', [
                'payment_id' => $payment->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->route('payments.history')
                ->with('error', 'Unable to load payment details.');
        }
    }

    /**
     * Process refund request (admin only)
     */
    public function processRefund(Request $request, Payment $payment)
    {
        try {
            // Check if user has permission to process refunds
            if (!Auth::user()->hasPermission('process_refunds')) {
                abort(403, 'Unauthorized to process refunds.');
            }

            $request->validate([
                'reason' => 'required|string|max:500'
            ]);

            // Mark payment as refunded
            $payment->markAsRefunded();
            $payment->update([
                'notes' => $request->reason,
                'metadata' => array_merge($payment->metadata ?? [], [
                    'refund_processed_by' => Auth::id(),
                    'refund_reason' => $request->reason,
                    'refund_processed_at' => now()->toISOString()
                ])
            ]);

            // Remove enrollment if exists
            Enrollment::where('user_id', $payment->user_id)
                ->where('course_id', $payment->course_id)
                ->delete();

            Log::info('Payment refund processed', [
                'payment_id' => $payment->id,
                'processed_by' => Auth::id(),
                'reason' => $request->reason
            ]);

            return redirect()->back()
                ->with('success', 'Refund processed successfully.');

        } catch (Exception $e) {
            Log::error('Refund processing failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Refund processing failed. Please try again.');
        }
    }

    /**
     * Show payment system maintenance page
     */
    public function maintenance(Course $course = null)
    {
        return view('maintenance.payment-system', compact('course'));
    }

    /**
     * Notify admin of critical payment issues
     */
    private function notifyAdminOfPaymentIssue(PayPalException $e, $course = null)
    {
        try {
            Log::critical('URGENT: PayPal account restricted - immediate attention required', [
                'error_code' => $e->getPayPalErrorCode(),
                'debug_id' => $e->getPayPalDebugId(),
                'course_id' => $course ? $course->id : null,
                'course_title' => $course ? $course->title : null,
                'user_id' => auth()->id(),
                'timestamp' => now()->toISOString(),
                'message' => 'PayPal merchant account is restricted. All payments are currently failing.'
            ]);
            
            // Send email notification to admin if configured
             if (config('app.admin_email')) {
                 try {
                     Notification::route('mail', config('app.admin_email'))
                         ->notify(new PaymentIssueNotification($e, $course));
                 } catch (Exception $mailError) {
                     Log::error('Failed to send admin email notification', [
                         'mail_error' => $mailError->getMessage()
                     ]);
                 }
             }
            
        } catch (Exception $notificationError) {
            Log::error('Failed to send admin notification', [
                'original_error' => $e->getMessage(),
                'notification_error' => $notificationError->getMessage()
            ]);
        }
    }

    /**
     * Check if webhook has already been processed (idempotency)
     */
    private function isDuplicateWebhook(string $transmissionId): bool
    {
        $cacheKey = "paypal_webhook_processed:{$transmissionId}";
        return cache()->has($cacheKey);
    }

    /**
     * Store webhook processing result for idempotency
     */
    private function storeWebhookResult(string $transmissionId, array $result): void
    {
        $cacheKey = "paypal_webhook_processed:{$transmissionId}";
        cache()->put($cacheKey, $result, 86400); // Store for 24 hours
    }
}
