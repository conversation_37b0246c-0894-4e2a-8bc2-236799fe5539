<?php $__env->startSection('title', $instructor->name . ' - Instructor Profile'); ?>
<?php $__env->startSection('description', 'Learn from ' . $instructor->name . ' at Escape Matrix Academy. View courses, experience, and student reviews.'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="flex flex-col md:flex-row items-center gap-8">
                <!-- Instructor Avatar -->
                <div class="flex-shrink-0">
                    <img src="<?php echo e($instructor->avatar ? asset('storage/' . $instructor->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($instructor->name) . '&background=4f46e5&color=fff&size=200'); ?>" 
                         alt="<?php echo e($instructor->name); ?>" 
                         class="w-32 h-32 md:w-40 md:h-40 rounded-full border-4 border-white shadow-lg object-cover">
                </div>
                
                <!-- Instructor Info -->
                <div class="text-center md:text-left">
                    <h1 class="text-4xl md:text-5xl font-bold mb-4"><?php echo e($instructor->name); ?></h1>
                    <p class="text-xl text-indigo-100 mb-4">Course Instructor</p>
                    <?php if($instructor->bio): ?>
                        <p class="text-lg text-indigo-100 max-w-2xl"><?php echo e($instructor->bio); ?></p>
                    <?php endif; ?>
                    
                    <!-- Stats -->
                    <div class="flex flex-wrap justify-center md:justify-start gap-6 mt-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold"><?php echo e($stats['total_courses']); ?></div>
                            <div class="text-indigo-200">Courses</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold"><?php echo e(number_format($stats['total_students'])); ?></div>
                            <div class="text-indigo-200">Students</div>
                        </div>
                        <?php if($stats['total_reviews'] > 0): ?>
                        <div class="text-center">
                            <div class="text-2xl font-bold"><?php echo e(number_format($stats['average_rating'], 1)); ?></div>
                            <div class="text-indigo-200">Rating</div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- About Section -->
        <?php if($instructor->about): ?>
        <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">About <?php echo e($instructor->name); ?></h2>
            <div class="prose max-w-none text-gray-600">
                <?php echo nl2br(e($instructor->about)); ?>

            </div>
        </div>
        <?php endif; ?>

        <!-- Experience Section -->
        <?php if($instructor->experience): ?>
        <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Experience</h2>
            <div class="prose max-w-none text-gray-600">
                <?php echo nl2br(e($instructor->experience)); ?>

            </div>
        </div>
        <?php endif; ?>

        <!-- Courses Section -->
        <div class="bg-white rounded-lg shadow-sm p-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-900">Courses by <?php echo e($instructor->name); ?></h2>
                <span class="text-gray-500"><?php echo e($stats['total_courses']); ?> <?php echo e(Str::plural('course', $stats['total_courses'])); ?></span>
            </div>
            
            <?php if($courses->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
                            <!-- Course Image -->
                            <div class="aspect-video bg-gradient-to-br from-indigo-500 to-purple-600 relative">
                                <?php if($course->thumbnail): ?>
                                    <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>" 
                                         alt="<?php echo e($course->title); ?>" 
                                         class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="flex items-center justify-center h-full">
                                        <svg class="w-16 h-16 text-white opacity-50" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Course Level Badge -->
                                <?php if($course->level): ?>
                                    <span class="absolute top-3 left-3 bg-white bg-opacity-90 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                                        <?php echo e(ucfirst($course->level)); ?>

                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Course Content -->
                            <div class="p-6">
                                <div class="mb-2">
                                    <?php if($course->category): ?>
                                        <span class="text-indigo-600 text-sm font-medium"><?php echo e($course->category->name); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                    <a href="<?php echo e(route('courses.show', $course->slug)); ?>" class="hover:text-indigo-600 transition-colors">
                                        <?php echo e($course->title); ?>

                                    </a>
                                </h3>
                                
                                <?php if($course->description): ?>
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($course->description); ?></p>
                                <?php endif; ?>
                                
                                <!-- Course Stats -->
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <span><?php echo e($course->enrollments_count); ?> <?php echo e(Str::plural('student', $course->enrollments_count)); ?></span>
                                    <?php if($course->duration): ?>
                                        <span><?php echo e($course->duration); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Price and CTA -->
                                <div class="flex items-center justify-between">
                                    <div class="text-lg font-bold text-gray-900">
                                        <?php if($course->price > 0): ?>
                                            $<?php echo e(number_format($course->price, 2)); ?>

                                        <?php else: ?>
                                            <span class="text-green-600">Free</span>
                                        <?php endif; ?>
                                    </div>
                                    <a href="<?php echo e(route('courses.show', $course->slug)); ?>" 
                                       class="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors">
                                        View Course
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <?php if($courses->hasPages()): ?>
                    <div class="mt-8">
                        <?php echo e($courses->links()); ?>

                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No courses available</h3>
                    <p class="text-gray-500">This instructor hasn't published any courses yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/instructors/show.blade.php ENDPATH**/ ?>