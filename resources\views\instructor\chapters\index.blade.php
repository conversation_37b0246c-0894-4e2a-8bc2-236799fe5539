@extends('layouts.app')

@section('title', 'Chapters - ' . $course->title)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('instructor.courses.show', $course) }}" 
                           class="text-gray-600 hover:text-gray-800">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Course Chapters</h1>
                            <p class="text-sm text-gray-600">{{ $course->title }}</p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('instructor.courses.show', $course) }}"
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-plus mr-2"></i>Add Chapter
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        @if($chapters->count() > 0)
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900">
                            {{ $chapters->count() }} {{ Str::plural('Chapter', $chapters->count()) }}
                        </h2>
                        <div class="text-sm text-gray-600">
                            Drag and drop to reorder chapters
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    <div id="chapters-list" class="space-y-4">
                        @foreach($chapters as $chapter)
                            <div class="chapter-item border border-gray-200 rounded-lg p-4 bg-white hover:shadow-md transition-shadow" 
                                 data-chapter-id="{{ $chapter->id }}">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4 flex-1">
                                        <!-- Drag Handle -->
                                        <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600">
                                            <i class="fas fa-grip-vertical"></i>
                                        </div>

                                        <!-- Chapter Number -->
                                        <div class="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium">
                                            {{ $chapter->sort_order }}
                                        </div>

                                        <!-- Chapter Info -->
                                        <div class="flex-1">
                                            <h3 class="font-semibold text-gray-900">{{ $chapter->title }}</h3>
                                            @if($chapter->description)
                                                <p class="text-sm text-gray-600 mt-1">{{ Str::limit($chapter->description, 100) }}</p>
                                            @endif
                                            <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                                <span>{{ $chapter->lectures_count }} lectures</span>
                                                <span>{{ $chapter->getFormattedDuration() }}</span>
                                                <span class="px-2 py-1 rounded-full {{ $chapter->is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                    {{ $chapter->is_published ? 'Published' : 'Draft' }}
                                                </span>
                                                @if($chapter->is_free_preview)
                                                    <span class="px-2 py-1 rounded-full bg-blue-100 text-blue-800">Free Preview</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('instructor.courses.chapters.show', [$course, $chapter]) }}" 
                                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            Manage
                                        </a>
                                        <a href="{{ route('instructor.courses.chapters.edit', [$course, $chapter]) }}" 
                                           class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                            Edit
                                        </a>
                                        
                                        <!-- Dropdown Menu -->
                                        <div class="relative">
                                            <button type="button" 
                                                    class="text-gray-400 hover:text-gray-600 dropdown-toggle"
                                                    data-chapter-id="{{ $chapter->id }}">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                                                <div class="py-1">
                                                    <button type="button" 
                                                            onclick="toggleChapterStatus('{{ $chapter->id }}')"
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-{{ $chapter->is_published ? 'eye-slash' : 'eye' }} mr-2"></i>
                                                        {{ $chapter->is_published ? 'Unpublish' : 'Publish' }}
                                                    </button>
                                                    <form method="POST" action="{{ route('instructor.courses.chapters.duplicate', [$course, $chapter]) }}" class="block">
                                                        @csrf
                                                        <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                            <i class="fas fa-copy mr-2"></i>Duplicate
                                                        </button>
                                                    </form>
                                                    <button type="button" 
                                                            onclick="moveChapter('{{ $chapter->id }}', 'up')"
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-arrow-up mr-2"></i>Move Up
                                                    </button>
                                                    <button type="button" 
                                                            onclick="moveChapter('{{ $chapter->id }}', 'down')"
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-arrow-down mr-2"></i>Move Down
                                                    </button>
                                                    @if($chapter->lectures_count === 0)
                                                        <button type="button" 
                                                                onclick="deleteChapter('{{ $chapter->id }}')"
                                                                class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                                            <i class="fas fa-trash mr-2"></i>Delete
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Lectures Preview -->
                                @if($chapter->lectures->count() > 0)
                                    <div class="mt-4 pt-4 border-t border-gray-100">
                                        <div class="space-y-2">
                                            @foreach($chapter->lectures->take(3) as $lecture)
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <span class="w-4 h-4 bg-gray-200 rounded-full flex items-center justify-center text-xs mr-3">
                                                        {{ $lecture->sort_order }}
                                                    </span>
                                                    <span class="flex-1">{{ $lecture->title }}</span>
                                                    <span class="px-2 py-1 text-xs rounded-full
                                                        @if($lecture->type === 'video') bg-red-100 text-red-800
                                                        @elseif($lecture->type === 'text') bg-blue-100 text-blue-800
                                                        @elseif($lecture->type === 'quiz') bg-green-100 text-green-800
                                                        @elseif($lecture->type === 'assignment') bg-purple-100 text-purple-800
                                                        @else bg-gray-100 text-gray-800 @endif">
                                                        {{ ucfirst($lecture->type) }}
                                                    </span>
                                                </div>
                                            @endforeach
                                            @if($chapter->lectures->count() > 3)
                                                <div class="text-sm text-gray-500 text-center">
                                                    +{{ $chapter->lectures->count() - 3 }} more lectures
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @else
                                    <div class="mt-4 pt-4 border-t border-gray-100 text-center">
                                        <p class="text-sm text-gray-500 mb-2">No lectures in this chapter yet.</p>
                                        <a href="{{ route('instructor.courses.chapters.lectures.create', [$course, $chapter]) }}" 
                                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            <i class="fas fa-plus mr-1"></i>Add First Lecture
                                        </a>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="bg-white rounded-lg shadow p-12 text-center">
                <i class="fas fa-book-open text-6xl text-gray-400 mb-6"></i>
                <h3 class="text-xl font-medium text-gray-900 mb-4">No chapters yet</h3>
                <p class="text-gray-600 mb-6">Start building your course by adding your first chapter.</p>
                <a href="{{ route('instructor.courses.show', $course) }}"
                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add First Chapter
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Chapter</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete this chapter? This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirm-delete" 
                        class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700">
                    Delete
                </button>
                <button id="cancel-delete" 
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sortable
    const chaptersList = document.getElementById('chapters-list');
    if (chaptersList) {
        new Sortable(chaptersList, {
            handle: '.drag-handle',
            animation: 150,
            onEnd: function(evt) {
                updateChapterOrder();
            }
        });
    }

    // Dropdown toggles
    document.querySelectorAll('.dropdown-toggle').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const dropdown = this.nextElementSibling;
            
            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdown) {
                    menu.classList.add('hidden');
                }
            });
            
            dropdown.classList.toggle('hidden');
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
    });

    // Delete modal
    const deleteModal = document.getElementById('delete-modal');
    const confirmDelete = document.getElementById('confirm-delete');
    const cancelDelete = document.getElementById('cancel-delete');
    let chapterToDelete = null;

    window.deleteChapter = function(chapterId) {
        chapterToDelete = chapterId;
        deleteModal.classList.remove('hidden');
    };

    confirmDelete.addEventListener('click', function() {
        if (chapterToDelete) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/instructor/courses/{{ $course->id }}/chapters/${chapterToDelete}`;
            form.innerHTML = `
                @csrf
                @method('DELETE')
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });

    cancelDelete.addEventListener('click', function() {
        deleteModal.classList.add('hidden');
        chapterToDelete = null;
    });

    // Update chapter order
    function updateChapterOrder() {
        const chapterIds = Array.from(chaptersList.children).map(item => 
            item.getAttribute('data-chapter-id')
        );

        fetch(`{{ route('instructor.courses.chapters.update-order', $course) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                chapters: chapterIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update chapter numbers
                chaptersList.querySelectorAll('.chapter-item').forEach((item, index) => {
                    const numberElement = item.querySelector('.w-8.h-8');
                    if (numberElement) {
                        numberElement.textContent = index + 1;
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error updating chapter order:', error);
            // Reload page on error
            location.reload();
        });
    }

    // Toggle chapter status
    window.toggleChapterStatus = function(chapterId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/instructor/courses/{{ $course->id }}/chapters/${chapterId}/toggle-status`;
        form.innerHTML = `
            @csrf
            @method('PATCH')
        `;
        document.body.appendChild(form);
        form.submit();
    };

    // Move chapter
    window.moveChapter = function(chapterId, direction) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/instructor/courses/{{ $course->id }}/chapters/${chapterId}/move-${direction}`;
        form.innerHTML = `
            @csrf
        `;
        document.body.appendChild(form);
        form.submit();
    };
});
</script>
@endpush
