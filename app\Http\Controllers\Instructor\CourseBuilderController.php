<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use App\Models\CourseCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CourseBuilderController extends Controller
{
    /**
     * Display the course builder interface.
     */
    public function show(Course $course)
    {
        $this->authorize('view', $course);

        $course->load([
            'category',
            'subcategory',
            'chapters.lectures' => function ($query) {
                $query->orderBy('sort_order');
            },
            'instructor'
        ]);

        $categories = CourseCategory::active()
            ->parents()
            ->with('activeChildren')
            ->orderBy('name')
            ->get();

        return view('instructor.course-builder.show', compact('course', 'categories'));
    }

    /**
     * Upload preview image for course.
     */
    public function uploadPreviewImage(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        try {
            $request->validate([
                'preview_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            ]);

            $file = $request->file('preview_image');
            
            // Delete old preview image if it exists and is a file path
            if ($course->preview_type === 'image' && $course->preview_content && !filter_var($course->preview_content, FILTER_VALIDATE_URL)) {
                Storage::disk('public')->delete($course->preview_content);
            }

            // Generate unique filename
            $filename = 'course-preview-' . $course->id . '-' . time() . '.' . $file->getClientOriginalExtension();
            
            // Store the file
            $path = $file->storeAs('courses/previews', $filename, 'public');

            // Update course with new preview image path
            $course->update([
                'preview_type' => 'image',
                'preview_content' => $path
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Preview image uploaded successfully',
                'data' => [
                    'preview_content' => $path,
                    'preview_url' => Storage::disk('public')->url($path)
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Debug: Log validation errors
            \Log::error('Auto-save validation failed:', $e->errors());
            
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload preview image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload course thumbnail image.
     */
    public function uploadCourseThumbnail(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        try {
            $request->validate([
                'course_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            ]);

            $file = $request->file('course_image');
            
            // Delete old course image if it exists
            if ($course->image && Storage::disk('public')->exists($course->image)) {
                Storage::disk('public')->delete($course->image);
            }

            // Generate unique filename
            $filename = 'course-thumbnail-' . $course->id . '-' . time() . '.' . $file->getClientOriginalExtension();
            
            // Store the file
            $path = $file->storeAs('courses/thumbnails', $filename, 'public');

            // Update course with new thumbnail image path
            $course->update([
                'image' => $path
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Course thumbnail uploaded successfully',
                'data' => [
                    'image' => $path,
                    'image_url' => Storage::disk('public')->url($path)
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload course thumbnail: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Auto-save course details.
     */
    public function autoSaveCourse(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        try {
            $validatedData = $request->validate([
                'title' => 'sometimes|required|string|max:255',
                'subtitle' => 'sometimes|nullable|string|max:255',
                'description' => 'sometimes|required|string|max:5000',
                'what_you_will_learn' => 'sometimes|nullable|array',
                'what_you_will_learn.*' => 'string|max:255',
                'what_youll_learn_text' => 'sometimes|nullable|string|max:10000',
                'requirements' => 'sometimes|nullable|array',
                'requirements.*' => 'string|max:255',
                'target_audience' => 'sometimes|nullable|array',
                'target_audience.*' => 'string|max:255',
                'category_id' => 'sometimes|required|exists:course_categories,id',
                'subcategory_id' => 'sometimes|nullable|exists:course_categories,id',
                'language' => 'sometimes|required|string|max:50',
                'level' => 'sometimes|required|in:beginner,intermediate,advanced,all_levels',
                'price' => 'sometimes|required|numeric|min:0|max:999.99',
                'original_price' => 'sometimes|nullable|numeric|min:0|max:999.99',
                'preview_type' => 'sometimes|nullable|in:image,video,youtube',
                'preview_content' => 'sometimes|nullable|string|max:500',
                'image' => 'sometimes|nullable|string|max:500',
            ]);

            // Sync category names for backward compatibility
            if (isset($validatedData['category_id'])) {
                $category = CourseCategory::find($validatedData['category_id']);
                $validatedData['category'] = $category ? $category->name : null;
            }
            if (isset($validatedData['subcategory_id'])) {
                $subcategory = CourseCategory::find($validatedData['subcategory_id']);
                $validatedData['subcategory'] = $subcategory ? $subcategory->name : null;
            }

            // Update slug if title changed
            if (isset($validatedData['title']) && $validatedData['title'] !== $course->title) {
                $validatedData['slug'] = $course->generateUniqueSlug($validatedData['title'], $course->id);
            }

            $course->update($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Course details saved automatically',
                'data' => $course->fresh()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save course details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new category dynamically.
     */
    public function createCategory(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255|unique:course_categories,name',
                'description' => 'nullable|string|max:1000',
                'icon' => 'nullable|string|max:100',
                'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            ]);

            // Set default values
            $validatedData['is_active'] = true;
            $validatedData['is_featured'] = false;
            $validatedData['sort_order'] = \App\Models\CourseCategory::whereNull('parent_id')->max('sort_order') + 1;

            // Set default icon and color if not provided
            if (!isset($validatedData['icon'])) {
                $validatedData['icon'] = 'fas fa-folder';
            }
            if (!isset($validatedData['color'])) {
                $validatedData['color'] = '#6B7280';
            }

            $category = \App\Models\CourseCategory::create($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Category created successfully',
                'data' => $category
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create category: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all active categories for dropdown.
     */
    public function getCategories()
    {
        try {
            $categories = \App\Models\CourseCategory::active()
                ->parents()
                ->with('activeChildren')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch categories: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Auto-save chapter details.
     */
    public function autoSaveChapter(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        
        if ($chapter->course_id !== $course->id) {
            return response()->json([
                'success' => false,
                'message' => 'Chapter does not belong to this course'
            ], 403);
        }

        try {
            $validatedData = $request->validate([
                'title' => 'sometimes|required|string|max:255',
                'description' => 'sometimes|nullable|string|max:2000',
                'learning_objectives' => 'sometimes|nullable|array',
                'learning_objectives.*' => 'string|max:255',
                'is_published' => 'sometimes|in:0,1,true,false',
            ]);

            // Process boolean fields properly
            if (isset($validatedData['is_published'])) {
                $validatedData['is_published'] = $this->parseBooleanValue($validatedData['is_published']);
            }

            // Update slug if title changed
            if (isset($validatedData['title']) && $validatedData['title'] !== $chapter->title) {
                $validatedData['slug'] = $chapter->generateUniqueSlug($validatedData['title'], $chapter->course_id, $chapter->id);
            }

            $chapter->update($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Chapter saved automatically',
                'data' => $chapter->fresh()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save chapter: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get lecture details for editing.
     */
    public function getLecture(Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);

        if ($chapter->course_id !== $course->id || $lecture->chapter_id !== $chapter->id) {
            return response()->json([
                'success' => false,
                'message' => 'Lecture does not belong to this course/chapter'
            ], 403);
        }

        // Convert lecture to array and map content field to text_content for frontend compatibility
        $lectureData = $lecture->toArray();
        if (isset($lectureData['content'])) {
            $lectureData['text_content'] = $lectureData['content'];
        }

        return response()->json([
            'success' => true,
            'data' => $lectureData
        ]);
    }

    /**
     * Get chapter details for editing.
     */
    public function getChapter(Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);

        if ($chapter->course_id !== $course->id) {
            return response()->json([
                'success' => false,
                'message' => 'Chapter does not belong to this course'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $chapter
        ]);
    }

    /**
     * Auto-save lecture details.
     */
    public function autoSaveLecture(Request $request, Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);

        if ($chapter->course_id !== $course->id || $lecture->chapter_id !== $chapter->id) {
            return response()->json([
                'success' => false,
                'message' => 'Lecture does not belong to this course/chapter'
            ], 403);
        }

        try {
            // Log the incoming request data for debugging
            \Log::info('Auto-save lecture request', [
                'lecture_id' => $lecture->id,
                'request_data' => $request->all()
            ]);

            // Build validation rules dynamically based on what's being sent
            $rules = [
                'title' => 'sometimes|required|string|max:255',
                'description' => 'sometimes|nullable|string|max:2000',
                'type' => 'sometimes|required|in:video,text,quiz,assignment,resource',
                'duration_minutes' => 'sometimes|nullable|integer|min:0|max:1440',
                'video_url' => 'sometimes|nullable|url',
                'content' => 'sometimes|nullable|string',
                'text_content' => 'sometimes|nullable|string', // Add text_content field
                'quiz_passing_score' => 'sometimes|nullable|integer|min:0|max:100',
                'quiz_instructions' => 'sometimes|nullable|string|max:5000',
                'estimated_completion_minutes' => 'sometimes|nullable|integer|min:0|max:1440',
                'assignment_instructions' => 'sometimes|nullable|string|max:10000',
                'assignment_max_points' => 'sometimes|nullable|integer|min:0|max:1000',
                'assignment_due_date' => 'sometimes|nullable|date',
                'resource_description' => 'sometimes|nullable|string|max:2000',
                'resource_url' => 'sometimes|nullable|url',
            ];

            // Handle file validation - the frontend always sends files as array due to multiple attribute
            if ($request->hasFile('resource_file')) {
                $files = $request->file('resource_file');
                $hasArrayFiles = is_array($files);

                \Log::info('File upload validation setup', [
                    'has_resource_file' => true,
                    'has_array_files' => $hasArrayFiles,
                    'file_type' => gettype($files),
                    'file_count' => $hasArrayFiles ? count($files) : 1,
                    'all_files_keys' => array_keys($request->allFiles())
                ]);

                if ($hasArrayFiles) {
                    // Files sent as array (resource_file[])
                    $rules['resource_file'] = 'sometimes|array';
                    $rules['resource_file.*'] = 'file|max:102400';
                } else {
                    // Single file
                    $rules['resource_file'] = 'sometimes|file|max:102400';
                }
            }
            
            \Log::info('Validation rules for files', [
                'rules' => array_filter($rules, function($key) {
                    return strpos($key, 'resource_file') === 0;
                }, ARRAY_FILTER_USE_KEY),
                'all_files' => $request->allFiles(),
                'has_resource_file' => $request->hasFile('resource_file'),
                'request_all' => $request->all(),
                'file_keys' => array_keys($request->allFiles())
            ]);

            // Handle boolean fields - they might be sent as strings or not sent at all
            $booleanFields = ['is_published', 'is_free_preview', 'is_mandatory', 'quiz_allow_retakes'];
            foreach ($booleanFields as $field) {
                if ($request->has($field)) {
                    $rules[$field] = 'sometimes|boolean';
                }
            }

            $validatedData = $request->validate($rules);

            // Process boolean fields properly for auto-save
            foreach ($booleanFields as $field) {
                if ($request->has($field)) {
                    $validatedData[$field] = $this->parseBooleanValue($request->input($field));
                } elseif (array_key_exists($field, $validatedData)) {
                    // If the field was validated but has a null/empty value, set to false
                    $validatedData[$field] = false;
                }
            }

            // Handle text_content field mapping
            if (isset($validatedData['text_content'])) {
                $validatedData['content'] = $validatedData['text_content'];
                unset($validatedData['text_content']);
            }

            // Handle numeric fields that cannot be null in database
            $numericFields = ['duration_minutes', 'quiz_passing_score', 'estimated_completion_minutes'];
            foreach ($numericFields as $field) {
                if (array_key_exists($field, $validatedData)) {
                    \Log::info("Processing numeric field: {$field}", [
                        'original_value' => $validatedData[$field],
                        'is_null' => is_null($validatedData[$field]),
                        'is_empty_string' => $validatedData[$field] === ''
                    ]);
                    // Convert null/empty values to 0 for database compatibility
                    if (is_null($validatedData[$field]) || $validatedData[$field] === '') {
                        $validatedData[$field] = 0;
                        \Log::info("Converted {$field} to 0");
                    }
                } else {
                    \Log::info("Field {$field} not in validated data");
                }
            }

            // Ensure duration_minutes is never null (database constraint)
            if (array_key_exists('duration_minutes', $validatedData) && is_null($validatedData['duration_minutes'])) {
                $validatedData['duration_minutes'] = 0;
                \Log::info("Force-converted duration_minutes from null to 0");
            }

            // Handle file uploads for resource type lectures
            \Log::info('Checking file upload conditions', [
                'has_file' => $request->hasFile('resource_file'),
                'type' => $request->input('type'),
                'lecture_type' => $lecture->type,
                'files' => $request->allFiles(),
                'all_input' => $request->all()
            ]);
            
            // Check for resource files (both single and multiple formats)
            $hasResourceFile = $request->hasFile('resource_file');
            
            $uploadedFiles = [];
            if ($hasResourceFile) {
                // Allow file upload for resource type lectures or when switching to resource type
                if ($request->input('type') === 'resource' || $lecture->type === 'resource') {
                    $uploadedFiles = $this->handleMultipleResourceFileUpload($request, $lecture, $course);
                }
            }

            // Handle type-specific fields that need to be stored in JSON columns
            $this->processTypeSpecificFields($validatedData, $lecture);

            // Update slug if title changed
            if (isset($validatedData['title']) && $validatedData['title'] !== $lecture->title) {
                $validatedData['slug'] = $lecture->generateUniqueLectureSlug($validatedData['title'], $lecture->chapter_id, $lecture->id);
            }

            \Log::info('Final validated data before update', [
                'validated_data' => $validatedData,
                'duration_minutes' => $validatedData['duration_minutes'] ?? 'NOT_SET'
            ]);

            $lecture->update($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Lecture saved automatically',
                'data' => $lecture->fresh(),
                'files' => $uploadedFiles // Include uploaded files for real-time updates
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Auto-save lecture validation failed', [
                'lecture_id' => $lecture->id,
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Auto-save lecture failed', [
                'lecture_id' => $lecture->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to save lecture: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process type-specific fields that need to be stored in JSON columns.
     */
    private function processTypeSpecificFields(array &$validatedData, Lecture $lecture)
    {
        // Handle quiz-specific fields
        if (isset($validatedData['quiz_instructions']) || isset($validatedData['quiz_passing_score']) || isset($validatedData['quiz_allow_retakes'])) {
            $quizData = $lecture->quiz_data ?? [];

            if (isset($validatedData['quiz_instructions'])) {
                $quizData['instructions'] = $validatedData['quiz_instructions'];
                unset($validatedData['quiz_instructions']);
            }

            $validatedData['quiz_data'] = $quizData;
        }

        // Handle assignment-specific fields
        if (isset($validatedData['assignment_instructions']) || isset($validatedData['assignment_max_points']) || isset($validatedData['assignment_due_date'])) {
            $attachments = $lecture->attachments ?? [];

            if (isset($validatedData['assignment_instructions'])) {
                $attachments['instructions'] = $validatedData['assignment_instructions'];
                unset($validatedData['assignment_instructions']);
            }

            if (isset($validatedData['assignment_max_points'])) {
                $attachments['max_points'] = (int) $validatedData['assignment_max_points'];
                unset($validatedData['assignment_max_points']);
            }

            if (isset($validatedData['assignment_due_date'])) {
                $attachments['due_date'] = $validatedData['assignment_due_date'];
                unset($validatedData['assignment_due_date']);
            }

            $validatedData['attachments'] = $attachments;
        }

        // Handle resource-specific fields
        if (isset($validatedData['resource_description']) || isset($validatedData['resource_url'])) {
            $resources = $lecture->resources ?? [];

            if (isset($validatedData['resource_description'])) {
                $resources['description'] = $validatedData['resource_description'];
                unset($validatedData['resource_description']);
            }

            if (isset($validatedData['resource_url'])) {
                $resources['url'] = $validatedData['resource_url'];
                unset($validatedData['resource_url']);
            }

            $validatedData['resources'] = $resources;
        }
    }

    /**
     * Handle resource file upload for lectures.
     */
    private function handleResourceFileUpload(Request $request, Lecture $lecture, Course $course)
    {
        try {
            \Log::info('Starting file upload process', [
                'lecture_id' => $lecture->id,
                'course_id' => $course->id,
                'instructor_id' => auth()->id(),
                'all_files' => $request->allFiles()
            ]);
            
            // Delete old resource files if they exist
            if ($lecture->resources && isset($lecture->resources['files'])) {
                foreach ($lecture->resources['files'] as $oldFile) {
                    if (isset($oldFile['file_path'])) {
                        Storage::disk('private')->delete($oldFile['file_path']);
                        \Log::info('Deleted old file', ['path' => $oldFile['file_path']]);
                    }
                }
            }

            // Handle both single file and multiple file formats
            $uploadedFiles = [];
            
            if ($request->hasFile('resource_file')) {
                $files = $request->file('resource_file');
                
                if (is_array($files)) {
                    // Multiple files sent as resource_file[]
                    $uploadedFiles = $files;
                    \Log::info('Processing multiple files from array format', [
                        'file_count' => count($files)
                    ]);
                } else {
                    // Single file
                    $uploadedFiles[] = $files;
                    \Log::info('Processing single file', [
                        'file_name' => $files->getClientOriginalName()
                    ]);
                }
            }
            
            \Log::info('Found uploaded files', [
                'count' => count($uploadedFiles),
                'files' => array_map(function($file) {
                    return $file->getClientOriginalName();
                }, $uploadedFiles)
            ]);

            $newFiles = [];
            foreach ($uploadedFiles as $file) {
                $directory = auth()->id() . "/{$course->id}/materials/resources";
                $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                
                \Log::info('Attempting to store file', [
                    'original_name' => $file->getClientOriginalName(),
                    'directory' => $directory,
                    'filename' => $filename,
                    'file_size' => $file->getSize()
                ]);
                
                $path = $file->storeAs($directory, $filename, 'private');
                
                \Log::info('File stored successfully', ['path' => $path]);

                $fileData = [
                    'name' => $file->getClientOriginalName(),
                    'file_path' => $path,
                    'file_size' => $file->getSize(),
                    'file_type' => $file->getClientMimeType(),
                    'uploaded_at' => now()->toISOString(),
                ];

                $newFiles[] = $fileData;
            }

            // Update lecture resources with new files
            $resources = $lecture->resources ?? [];
            $resources['files'] = $newFiles;
            $lecture->update(['resources' => $resources]);

            \Log::info('Resource files uploaded successfully', [
                'lecture_id' => $lecture->id,
                'file_count' => count($newFiles),
                'files' => array_column($newFiles, 'name')
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to upload resource file', [
                'lecture_id' => $lecture->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function handleMultipleResourceFileUpload($request, $lecture, $course)
    {
        if (!$request->hasFile('resource_file')) {
            return [];
        }

        try {
            // Keep existing files - we're now using additive file management
            \Log::info('Using additive file management - preserving existing files', [
                'lecture_id' => $lecture->id,
                'existing_files_count' => isset($lecture->resources['files']) ? count($lecture->resources['files']) : 0
            ]);

            // Handle both single file and multiple file formats
            $uploadedFiles = [];

            if ($request->hasFile('resource_file')) {
                $files = $request->file('resource_file');

                if (is_array($files)) {
                    // Multiple files sent as resource_file[]
                    $uploadedFiles = $files;
                    \Log::info('Processing multiple files from array format', [
                        'file_count' => count($files)
                    ]);
                } else {
                    // Single file
                    $uploadedFiles[] = $files;
                    \Log::info('Processing single file', [
                        'file_name' => $files->getClientOriginalName()
                    ]);
                }
            }

            \Log::info('Found uploaded files for real-time processing', [
                'count' => count($uploadedFiles),
                'files' => array_map(function($file) {
                    return $file->getClientOriginalName();
                }, $uploadedFiles)
            ]);

            $newFiles = [];
            foreach ($uploadedFiles as $file) {
                $directory = auth()->id() . "/{$course->id}/materials/resources";
                $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

                \Log::info('Attempting to store file for real-time update', [
                    'original_name' => $file->getClientOriginalName(),
                    'directory' => $directory,
                    'filename' => $filename,
                    'file_size' => $file->getSize()
                ]);

                $path = $file->storeAs($directory, $filename, 'private');

                \Log::info('File stored successfully for real-time update', ['path' => $path]);

                $fileData = [
                    'id' => uniqid(),
                    'name' => $file->getClientOriginalName(),
                    'original_name' => $file->getClientOriginalName(),
                    'file_path' => $path,
                    'size' => $file->getSize(),
                    'file_size' => $file->getSize(),
                    'file_type' => $file->getClientMimeType(),
                    'uploaded_at' => now()->toISOString(),
                    'download_url' => route('files.download-course-material', [
                        'courseId' => $course->id,
                        'filename' => $filename
                    ])
                ];

                $newFiles[] = $fileData;
            }

            // Update lecture resources with new files (additive approach)
            $resources = $lecture->resources ?? [];
            $existingFiles = $resources['files'] ?? [];

            // Append new files to existing ones
            $resources['files'] = array_merge($existingFiles, $newFiles);
            $lecture->update(['resources' => $resources]);

            \Log::info('Files added to existing collection', [
                'existing_count' => count($existingFiles),
                'new_count' => count($newFiles),
                'total_count' => count($resources['files'])
            ]);

            \Log::info('Resource files uploaded successfully for real-time update', [
                'lecture_id' => $lecture->id,
                'new_file_count' => count($newFiles),
                'total_file_count' => count($resources['files']),
                'new_files' => array_column($newFiles, 'name')
            ]);

            // Return all files (existing + new) for complete display update
            return $resources['files'];

        } catch (\Exception $e) {
            \Log::error('Failed to upload multiple resource files', [
                'lecture_id' => $lecture->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Delete a specific file from a lecture.
     */
    public function deleteFile(Course $course, Chapter $chapter, Lecture $lecture, $fileId)
    {
        try {
            \Log::info('File deletion request received', [
                'course_id' => $course->id,
                'course_slug' => $course->slug,
                'chapter_id' => $chapter->id,
                'lecture_id' => $lecture->id,
                'file_id' => $fileId,
                'user_id' => auth()->id()
            ]);

            // Ensure the user owns this course
            if ($course->instructor_id !== auth()->id()) {
                \Log::warning('Unauthorized file deletion attempt', [
                    'course_instructor_id' => $course->instructor_id,
                    'requesting_user_id' => auth()->id()
                ]);
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Get current resources (direct array of files)
            $resources = $lecture->resources ?? [];

            // Find the file to delete
            $fileToDelete = null;
            $updatedFiles = [];

            foreach ($resources as $file) {
                if (isset($file['id']) && $file['id'] === $fileId) {
                    $fileToDelete = $file;
                } else {
                    $updatedFiles[] = $file;
                }
            }

            if (!$fileToDelete) {
                \Log::warning('File not found for deletion', [
                    'file_id' => $fileId,
                    'lecture_id' => $lecture->id,
                    'available_files' => array_column($resources, 'id'),
                    'resources_structure' => $resources
                ]);
                return response()->json(['error' => 'File not found'], 404);
            }

            // Delete the physical file from storage
            if (isset($fileToDelete['file_path'])) {
                Storage::disk('private')->delete($fileToDelete['file_path']);
                \Log::info('Deleted file from storage', [
                    'file_path' => $fileToDelete['file_path'],
                    'lecture_id' => $lecture->id
                ]);
            }

            // Update the lecture with remaining files (direct array)
            $lecture->update(['resources' => $updatedFiles]);

            \Log::info('File deleted successfully', [
                'file_id' => $fileId,
                'file_name' => $fileToDelete['name'] ?? 'unknown',
                'lecture_id' => $lecture->id,
                'remaining_files' => count($updatedFiles)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully',
                'data' => [
                    'files' => $updatedFiles,
                    'total_files' => count($updatedFiles)
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to delete file', [
                'file_id' => $fileId,
                'lecture_id' => $lecture->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to delete file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Parse boolean values from form data.
     */
    private function parseBooleanValue($value)
    {
        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            return in_array(strtolower($value), ['1', 'true', 'on', 'yes']);
        }

        return (bool) $value;
    }

    /**
     * Create a new chapter.
     */
    public function createChapter(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        try {
            $validatedData = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string|max:2000',
                'learning_objectives' => 'nullable|array',
                'learning_objectives.*' => 'string|max:255',
                'is_free_preview' => 'boolean',
            ]);

            DB::beginTransaction();

            // Get the next sort order
            $maxSortOrder = $course->chapters()->max('sort_order') ?? 0;

            $chapter = Chapter::create([
                'id' => Str::uuid(),
                'title' => $validatedData['title'],
                'slug' => $this->generateUniqueSlug($validatedData['title'], $course->id),
                'description' => $validatedData['description'] ?? null,
                'learning_objectives' => $validatedData['learning_objectives'] ?? [],
                'course_id' => $course->id,
                'instructor_id' => auth()->id(),
                'sort_order' => $maxSortOrder + 1,
                'is_published' => false,
                'is_free_preview' => $validatedData['is_free_preview'] ?? false,
                'total_duration_minutes' => 0,
                'total_lectures' => 0,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Chapter created successfully',
                'data' => $chapter->fresh()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create chapter: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new lecture.
     */
    public function createLecture(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        
        if ($chapter->course_id !== $course->id) {
            return response()->json([
                'success' => false,
                'message' => 'Chapter does not belong to this course'
            ], 403);
        }

        try {
            $validatedData = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string|max:2000',
                'type' => 'required|in:video,text,quiz,assignment,resource',
                'duration_minutes' => 'nullable|integer|min:0|max:1440',
                'is_free_preview' => 'boolean',
                'is_mandatory' => 'boolean',
                'video_url' => 'nullable|url',
                'content' => 'nullable|string',
                'quiz_passing_score' => 'nullable|integer|min:0|max:100',
                'quiz_allow_retakes' => 'boolean',
                'estimated_completion_minutes' => 'nullable|integer|min:0|max:1440',
            ]);

            DB::beginTransaction();

            // Get the next sort order
            $maxSortOrder = $chapter->lectures()->max('sort_order') ?? 0;

            $lecture = Lecture::create([
                'id' => Str::uuid(),
                'title' => $validatedData['title'],
                'slug' => $this->generateUniqueLectureSlug($validatedData['title'], $chapter->id),
                'description' => $validatedData['description'] ?? null,
                'type' => $validatedData['type'],
                'chapter_id' => $chapter->id,
                'course_id' => $course->id,
                'instructor_id' => auth()->id(),
                'sort_order' => $maxSortOrder + 1,
                'is_published' => false,
                'is_free_preview' => $validatedData['is_free_preview'] ?? false,
                'is_mandatory' => $validatedData['is_mandatory'] ?? true,
                'duration_minutes' => $validatedData['duration_minutes'] ?? 0,
                'content' => $validatedData['content'] ?? null,
                'video_url' => $validatedData['video_url'] ?? null,
                'quiz_passing_score' => $validatedData['quiz_passing_score'] ?? null,
                'quiz_allow_retakes' => $validatedData['quiz_allow_retakes'] ?? false,
                'estimated_completion_minutes' => $validatedData['estimated_completion_minutes'] ?? null,
            ]);

            // Update chapter statistics
            $chapter->updateStatistics();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Lecture created successfully',
                'data' => $lecture->fresh()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create lecture: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a chapter.
     */
    public function deleteChapter(Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);

        if ($chapter->course_id !== $course->id) {
            return response()->json([
                'success' => false,
                'message' => 'Chapter does not belong to this course'
            ], 403);
        }

        try {
            DB::beginTransaction();

            // Delete all lectures in the chapter
            $chapter->lectures()->delete();

            // Delete the chapter
            $chapter->delete();

            // Update course statistics
            $course->updateStatistics();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Chapter deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete chapter: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a lecture.
     */
    public function deleteLecture(Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);

        if ($chapter->course_id !== $course->id || $lecture->chapter_id !== $chapter->id) {
            return response()->json([
                'success' => false,
                'message' => 'Lecture does not belong to this course/chapter'
            ], 403);
        }

        try {
            DB::beginTransaction();

            // Delete the lecture
            $lecture->delete();

            // Update chapter and course statistics
            $chapter->updateStatistics();
            $course->updateStatistics();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Lecture deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete lecture: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder chapters.
     */
    public function reorderChapters(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        try {
            $validatedData = $request->validate([
                'chapter_ids' => 'required|array',
                'chapter_ids.*' => 'required|string|exists:chapters,id',
            ]);

            DB::beginTransaction();

            foreach ($validatedData['chapter_ids'] as $index => $chapterId) {
                Chapter::where('id', $chapterId)
                    ->where('course_id', $course->id)
                    ->update(['sort_order' => $index + 1]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Chapters reordered successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder chapters: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder lectures within a chapter.
     */
    public function reorderLectures(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);

        if ($chapter->course_id !== $course->id) {
            return response()->json([
                'success' => false,
                'message' => 'Chapter does not belong to this course'
            ], 403);
        }

        try {
            $validatedData = $request->validate([
                'lecture_ids' => 'required|array',
                'lecture_ids.*' => 'required|string|exists:lectures,id',
            ]);

            DB::beginTransaction();

            foreach ($validatedData['lecture_ids'] as $index => $lectureId) {
                Lecture::where('id', $lectureId)
                    ->where('chapter_id', $chapter->id)
                    ->update(['sort_order' => $index + 1]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Lectures reordered successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder lectures: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload resources for a lecture.
     */
    public function uploadLectureResources(Request $request, Course $course, Chapter $chapter, Lecture $lecture)
    {
        try {
            $this->authorize('update', $course);

            // Validate the request
            $request->validate([
                'resource_files' => 'required|array|max:10',
                'resource_files.*' => 'file|max:51200|mimes:pdf,doc,docx,ppt,pptx,xls,xlsx,zip,rar,jpg,jpeg,png,gif,txt,csv'
            ]);

            \Log::info('Starting resource file upload', [
                'lecture_id' => $lecture->id,
                'course_id' => $course->id,
                'file_count' => count($request->file('resource_files'))
            ]);

            $uploadedFiles = [];
            $directory = "courses/{$course->instructor_id}/{$course->id}/materials/resources";

            foreach ($request->file('resource_files') as $file) {
                if ($file && $file->isValid()) {
                    $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                    $path = $file->storeAs($directory, $filename, 'private');

                    $fileData = [
                        'id' => uniqid(),
                        'name' => $file->getClientOriginalName(),
                        'original_name' => $file->getClientOriginalName(),
                        'file_path' => $path,
                        'file_size' => $file->getSize(),
                        'file_type' => $file->getClientMimeType(),
                        'uploaded_at' => now()->toISOString(),
                        'download_url' => route('files.download-course-material', [
                            'courseId' => $course->id,
                            'filename' => $filename
                        ])
                    ];

                    $uploadedFiles[] = $fileData;

                    \Log::info('File uploaded successfully', [
                        'filename' => $filename,
                        'original_name' => $file->getClientOriginalName(),
                        'size' => $file->getSize()
                    ]);
                }
            }

            // Get existing resources and merge with new ones
            $existingResources = $lecture->resources ?? [];
            $allResources = array_merge($existingResources, $uploadedFiles);

            // Update lecture with new resources
            $lecture->update(['resources' => $allResources]);

            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully',
                'data' => [
                    'files' => $uploadedFiles,
                    'total_files' => count($allResources)
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            \Log::error('Error uploading lecture resources', [
                'lecture_id' => $lecture->id,
                'course_id' => $course->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Publish/unpublish course.
     */
    public function togglePublishStatus(Course $course)
    {
        $this->authorize('update', $course);

        try {
            $newStatus = $course->status === 'published' ? 'draft' : 'published';

            $course->update([
                'status' => $newStatus,
                'published_at' => $newStatus === 'published' ? now() : null
            ]);

            return response()->json([
                'success' => true,
                'message' => $newStatus === 'published' ? 'Course published successfully' : 'Course unpublished successfully',
                'status' => $newStatus
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update course status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate a unique slug for the chapter within the course.
     */
    private function generateUniqueSlug(string $title, string $courseId, ?string $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = Chapter::where('course_id', $courseId)->where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Generate a unique slug for a lecture within the chapter.
     */
    private function generateUniqueLectureSlug(string $title, string $chapterId, ?string $excludeId = null): string
    {
        $lectureSlug = Str::slug($title);
        $baseSlug = $lectureSlug;
        $slug = $baseSlug;
        $counter = 1;

        while (true) {
            $query = Lecture::where('chapter_id', $chapterId)->where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
