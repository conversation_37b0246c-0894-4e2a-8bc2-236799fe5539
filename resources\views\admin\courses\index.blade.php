@extends('layouts.app')

@section('title', 'Course Monitoring - Admin')

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                    <div>
                        <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-white">Course <span class="text-red-500">Monitoring</span></h1>
                        <p class="mt-2 text-base md:text-lg text-gray-400">Monitor and moderate all course content across the platform</p>
                    </div>

                    <div class="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3">
                        <a href="{{ route('admin.dashboard') }}"
                           class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-3 md:px-6 md:py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center gap-2 border border-gray-600 text-sm md:text-base">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back to Dashboard</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-2 bg-gray-700 rounded-lg">
                        <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Courses</p>
                        <p class="text-2xl font-semibold text-white">{{ number_format($stats['total_courses']) }}</p>
                        <p class="text-sm text-blue-400">{{ $stats['published_courses'] }} published</p>
                    </div>
                </div>
            </div>

            <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-2 bg-gray-700 rounded-lg">
                        <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Enrollments</p>
                        <p class="text-2xl font-semibold text-white">{{ number_format($stats['total_enrollments']) }}</p>
                        <p class="text-sm text-green-400">{{ $stats['total_instructors'] }} instructors</p>
                    </div>
                </div>
            </div>

            <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-2 bg-gray-700 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Average Rating</p>
                        <p class="text-2xl font-semibold text-white">{{ number_format($stats['average_rating'], 1) }}</p>
                        <p class="text-sm text-yellow-400">{{ $stats['featured_courses'] }} featured</p>
                    </div>
                </div>
            </div>

            <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-2 bg-gray-700 rounded-lg">
                        <svg class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Revenue</p>
                        <p class="text-2xl font-semibold text-white">${{ number_format($stats['total_revenue'], 2) }}</p>
                        <p class="text-sm text-red-400">Platform earnings</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6 mb-6">
            <form method="GET" action="{{ route('admin.courses.index') }}" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-300">Search</label>
                        <input type="text" name="search" id="search"
                               value="{{ request('search') }}"
                               class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 placeholder-gray-400"
                               placeholder="Search courses, instructors...">
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-300">Status</label>
                        <select name="status" id="status"
                                class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                            <option value="">All Statuses</option>
                            @foreach($statuses as $status)
                                <option value="{{ $status }}" {{ request('status') === $status ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('_', ' ', $status)) }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label for="category_id" class="block text-sm font-medium text-gray-300">Category</label>
                        <select name="category_id" id="category_id"
                                class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label for="instructor_id" class="block text-sm font-medium text-gray-300">Instructor</label>
                        <select name="instructor_id" id="instructor_id"
                                class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                            <option value="">All Instructors</option>
                            @foreach($instructors as $instructor)
                                <option value="{{ $instructor->id }}" {{ request('instructor_id') == $instructor->id ? 'selected' : '' }}>
                                    {{ $instructor->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label for="featured" class="block text-sm font-medium text-gray-300">Featured</label>
                        <select name="featured" id="featured"
                                class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                            <option value="">All Courses</option>
                            <option value="1" {{ request('featured') === '1' ? 'selected' : '' }}>Featured Only</option>
                            <option value="0" {{ request('featured') === '0' ? 'selected' : '' }}>Non-Featured</option>
                        </select>
                    </div>

                    <div class="flex items-end">
                        <button type="submit"
                                class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </div>

                @if(request()->hasAny(['search', 'status', 'category_id', 'instructor_id', 'featured']))
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-gray-400">
                            Showing {{ $courses->count() }} of {{ $courses->total() }} courses
                        </p>
                        <a href="{{ route('admin.courses.index') }}"
                           class="text-sm text-red-500 hover:text-red-400">
                            Clear filters
                        </a>
                    </div>
                @endif
            </form>
        </div>

        <!-- Courses Grid -->
        @if($courses->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                @foreach($courses as $course)
                    <div class="bg-gray-800 border border-gray-700 rounded-lg shadow-md overflow-hidden hover:shadow-lg hover:border-red-500 transition-all">
                        <!-- Course Image -->
                        <div class="relative h-48 bg-gray-900">
                            @if($course->image)
                                <img src="{{ Storage::disk('private')->url($course->image) }}"
                                     alt="{{ $course->title }}"
                                     class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <i class="fas fa-book text-4xl text-gray-500"></i>
                                </div>
                            @endif

                            <!-- Status Badge -->
                            <div class="absolute top-3 left-3">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    @if($course->status === 'published') bg-green-600 text-green-100
                                    @elseif($course->status === 'draft') bg-yellow-600 text-yellow-100
                                    @elseif($course->status === 'under_review') bg-blue-600 text-blue-100
                                    @elseif($course->status === 'suspended') bg-red-600 text-red-100
                                    @else bg-gray-600 text-gray-100 @endif">
                                    {{ ucfirst(str_replace('_', ' ', $course->status)) }}
                                </span>
                            </div>

                            @if($course->featured)
                                <div class="absolute top-3 right-3">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-600 text-red-100">
                                        Featured
                                    </span>
                                </div>
                            @endif

                            <!-- Admin Actions Overlay -->
                            <div class="absolute bottom-3 right-3 flex space-x-2">
                                <a href="{{ route('admin.courses.show', $course) }}"
                                   class="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full transition-colors"
                                   title="View Details">
                                    <i class="fas fa-eye text-sm"></i>
                                </a>
                                <a href="{{ route('admin.courses.content', $course) }}"
                                   class="bg-green-600 hover:bg-green-700 text-white p-2 rounded-full transition-colors"
                                   title="Review Content">
                                    <i class="fas fa-search text-sm"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Course Content -->
                        <div class="p-6">
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">
                                    {{ $course->title }}
                                </h3>
                                @if($course->subtitle)
                                    <p class="text-sm text-gray-400 line-clamp-2">{{ $course->subtitle }}</p>
                                @endif
                            </div>

                            <!-- Instructor Info -->
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white text-sm font-bold">{{ substr($course->instructor->name, 0, 1) }}</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-white">{{ $course->instructor->name }}</p>
                                    <p class="text-xs text-gray-400">{{ $course->instructor->email }}</p>
                                </div>
                            </div>

                            <!-- Course Stats -->
                            <div class="grid grid-cols-2 gap-4 mb-4 text-sm text-gray-400">
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-2"></i>
                                    {{ $course->enrollments_count }} students
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-book-open mr-2"></i>
                                    {{ $course->chapters_count }} chapters
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-play-circle mr-2"></i>
                                    {{ $course->lectures_count }} lectures
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-star mr-2"></i>
                                    {{ number_format($course->reviews_avg_rating ?: 0, 1) }} rating
                                </div>
                            </div>

                            <!-- Price -->
                            <div class="mb-4">
                                @if($course->hasDiscount())
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-500 line-through">${{ number_format($course->original_price, 2, '.', '') }}</span>
                                        <span class="text-lg font-bold text-red-400 ml-2">${{ number_format($course->price, 2, '.', '') }}</span>
                                        <span class="bg-red-500 text-white text-xs px-1 py-0.5 rounded ml-1">{{ $course->getDiscountPercentage() }}% OFF</span>
                                    </div>
                                @elseif($course->isFree())
                                    <span class="text-lg font-bold text-green-400">Free</span>
                                @else
                                    <span class="text-lg font-bold text-white">${{ number_format($course->price, 2, '.', '') }}</span>
                                @endif
                            </div>

                            <!-- Admin Actions -->
                            <div class="flex space-x-2">
                                <a href="{{ route('admin.courses.content', $course) }}"
                                   class="flex-1 bg-red-600 hover:bg-red-700 text-white text-center px-3 py-2 rounded text-sm font-medium transition-colors">
                                    <i class="fas fa-search mr-1"></i>Review Content
                                </a>
                                <a href="{{ route('admin.courses.show', $course) }}"
                                   class="bg-gray-600 hover:bg-gray-700 text-white text-center px-3 py-2 rounded text-sm font-medium transition-colors"
                                   title="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="flex justify-center">
                {{ $courses->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <i class="fas fa-search text-6xl text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-semibold text-white mb-2">No courses found</h3>
                    <p class="text-gray-400 mb-6">Try adjusting your search criteria or filters.</p>
                    <a href="{{ route('admin.courses.index') }}"
                       class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Clear Filters
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection