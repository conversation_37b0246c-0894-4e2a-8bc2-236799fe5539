<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Exceptions\PayPalException;

class PaymentIssueNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $paypalException;
    protected $course;
    protected $additionalContext;

    /**
     * Create a new notification instance.
     */
    public function __construct(PayPalException $paypalException, $course = null, array $additionalContext = [])
    {
        $this->paypalException = $paypalException;
        $this->course = $course;
        $this->additionalContext = $additionalContext;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $errorCode = $this->paypalException->getPayPalErrorCode();
        $debugId = $this->paypalException->getPayPalDebugId();
        
        $subject = $this->getSubjectByErrorCode($errorCode);
        $priority = $this->getPriorityByErrorCode($errorCode);
        
        $mailMessage = (new MailMessage)
            ->subject($subject)
            ->greeting('🚨 Critical Payment System Alert')
            ->line($this->getMainMessage($errorCode))
            ->line('**Error Details:**')
            ->line('• Error Code: ' . $errorCode)
            ->line('• Debug ID: ' . ($debugId ?: 'N/A'))
            ->line('• Timestamp: ' . now()->format('Y-m-d H:i:s T'))
            ->line('• User ID: ' . (auth()->id() ?: 'Guest'));
            
        if ($this->course) {
            $mailMessage->line('• Course: ' . $this->course->title . ' (ID: ' . $this->course->id . ')');
        }
        
        $mailMessage->line('')
            ->line('**Immediate Actions Required:**')
            ->line($this->getActionItems($errorCode))
            ->line('')
            ->line('**Technical Details:**')
            ->line('• Error Message: ' . $this->paypalException->getMessage())
            ->line('• PayPal Response: ' . json_encode($this->paypalException->getAdditionalContext(), JSON_PRETTY_PRINT))
            ->action('View PayPal Dashboard', 'https://www.paypal.com/businessmanage/account/overview')
            ->line('This is an automated alert. Please investigate immediately.');
            
        // Set priority based on error severity
        if ($priority === 'high') {
            $mailMessage->priority(1); // High priority
        }
        
        return $mailMessage;
    }

    /**
     * Get subject line based on error code
     */
    private function getSubjectByErrorCode(string $errorCode): string
    {
        $subjects = [
            'PAYEE_ACCOUNT_RESTRICTED' => '🚨 URGENT: PayPal Account Restricted - All Payments Failing',
            'PAYEE_ACCOUNT_LOCKED_OR_CLOSED' => '🚨 CRITICAL: PayPal Account Locked/Closed',
            'PAYEE_ACCOUNT_INVALID' => '🚨 CRITICAL: PayPal Account Invalid',
            'INSTRUMENT_DECLINED' => '⚠️ Payment Processing Issues Detected',
            'INSUFFICIENT_FUNDS' => '⚠️ Customer Payment Issues Detected',
        ];
        
        return $subjects[$errorCode] ?? '⚠️ PayPal Payment System Alert: ' . $errorCode;
    }
    
    /**
     * Get priority level based on error code
     */
    private function getPriorityByErrorCode(string $errorCode): string
    {
        $highPriorityErrors = [
            'PAYEE_ACCOUNT_RESTRICTED',
            'PAYEE_ACCOUNT_LOCKED_OR_CLOSED',
            'PAYEE_ACCOUNT_INVALID'
        ];
        
        return in_array($errorCode, $highPriorityErrors) ? 'high' : 'normal';
    }
    
    /**
     * Get main message based on error code
     */
    private function getMainMessage(string $errorCode): string
    {
        $messages = [
            'PAYEE_ACCOUNT_RESTRICTED' => 'Your PayPal merchant account has been restricted. This means ALL payment processing is currently disabled and customers cannot complete purchases.',
            'PAYEE_ACCOUNT_LOCKED_OR_CLOSED' => 'Your PayPal merchant account appears to be locked or closed. Immediate attention required.',
            'PAYEE_ACCOUNT_INVALID' => 'PayPal is reporting that your merchant account configuration is invalid.',
            'INSTRUMENT_DECLINED' => 'Multiple customer payments are being declined. This may indicate an issue with your payment processing setup.',
            'INSUFFICIENT_FUNDS' => 'Customers are experiencing insufficient funds errors. Monitor for patterns that might indicate pricing or currency issues.',
        ];
        
        return $messages[$errorCode] ?? 'A PayPal payment error has occurred that requires administrative attention.';
    }
    
    /**
     * Get action items based on error code
     */
    private function getActionItems(string $errorCode): string
    {
        $actions = [
            'PAYEE_ACCOUNT_RESTRICTED' => "1. Log into PayPal Business Dashboard immediately\n2. Check for any account limitations or required actions\n3. Contact PayPal Business Support if no clear resolution\n4. Consider enabling maintenance mode if issue persists\n5. Communicate with customers about temporary payment issues",
            'PAYEE_ACCOUNT_LOCKED_OR_CLOSED' => "1. Contact PayPal Business Support immediately\n2. Verify account status in PayPal Dashboard\n3. Check for any compliance or verification requirements\n4. Enable maintenance mode until resolved",
            'PAYEE_ACCOUNT_INVALID' => "1. Verify PayPal API credentials in application config\n2. Check PayPal app settings and permissions\n3. Test with PayPal sandbox environment\n4. Contact PayPal Developer Support if needed",
        ];
        
        return $actions[$errorCode] ?? "1. Investigate the specific error in PayPal Dashboard\n2. Check application logs for patterns\n3. Test payment flow manually\n4. Contact PayPal support if issue persists";
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'error_code' => $this->paypalException->getPayPalErrorCode(),
            'debug_id' => $this->paypalException->getPayPalDebugId(),
            'course_id' => $this->course ? $this->course->id : null,
            'timestamp' => now()->toISOString(),
            'requires_immediate_attention' => $this->getPriorityByErrorCode($this->paypalException->getPayPalErrorCode()) === 'high'
        ];
    }
}