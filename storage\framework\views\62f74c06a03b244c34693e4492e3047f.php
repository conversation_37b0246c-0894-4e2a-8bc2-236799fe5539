<?php $__env->startSection('title', $title ?? 'Error - Escape Matrix Academy'); ?>
<?php $__env->startSection('description', $description ?? 'An error occurred while processing your request.'); ?>

<?php $__env->startPush('styles'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/css/errors.css']); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="error-container">
    <div class="error-card">
        <?php echo $__env->yieldContent('error-content'); ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/errors/layout.blade.php ENDPATH**/ ?>