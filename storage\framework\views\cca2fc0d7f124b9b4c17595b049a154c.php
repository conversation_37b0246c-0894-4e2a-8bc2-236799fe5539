

<?php $__env->startSection('title', 'Escape Matrix - AI Automation & Business Courses | Transform Your Life'); ?>
<?php $__env->startSection('description', 'Break free from limitations with Escape Matrix Academy. Master AI automation, business strategies, programming, mindset development, finance, marketing, and more. Join 1000+ students transforming their lives through our comprehensive online courses.'); ?>
<?php $__env->startSection('keywords', 'escape matrix, ai automation courses, business courses, programming courses, mindset development, finance courses, marketing courses, data science courses, cybersecurity training, cloud computing, online learning, transformation, education, skill development'); ?>

<?php $__env->startSection('og_title', 'Escape Matrix Academy - Master AI Automation & Business Skills'); ?>
<?php $__env->startSection('og_description', 'Transform your life with expert-led courses in AI automation, business, programming, mindset, and more. Join 1000+ students who escaped limitations.'); ?>

<?php $__env->startSection('twitter_title', 'Escape Matrix Academy - AI Automation & Business Courses'); ?>
<?php $__env->startSection('twitter_description', 'Break free from limitations. Master AI automation, business, programming & mindset development. Join 1000+ transformed students.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="relative py-20 md:py-32 bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fillRule=\'evenodd\'%3E%3Cg fill=\'%23ef4444\' fillOpacity=\'0.1\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'2\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    <div class="container mx-auto px-4 relative">
        <div class="max-w-4xl mx-auto text-center">
            <div class="inline-flex items-center px-4 py-2 bg-red-600/20 border border-red-600/30 rounded-full text-red-400 text-sm mb-8">
                <span class="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
                Join 1000+ Students Who Escaped the Matrix
            </div>

            <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                Break Free from
                <br />
                <span class="text-red-500 bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
                    Limitations
                </span>
            </h1>

            <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                Master the skills that matter. Transform your mindset. Build your empire. 
                The Escape Matrix Academy provides the tools, knowledge, and community to break free from ordinary.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <a href="<?php echo e(route('courses.index')); ?>" class="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-md text-lg font-semibold transition-colors inline-flex items-center justify-center">
                    Start Your Transformation
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </a>
                <!-- <a href="#demo" class="border border-gray-600 text-white hover:bg-gray-800 px-8 py-4 rounded-md text-lg font-semibold transition-colors inline-flex items-center justify-center">
                    <svg class="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Watch Demo
                </a> -->
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
                <div class="text-center">
                    <div class="text-3xl font-bold text-red-500 mb-2">AI & Tech</div>
                    <p class="text-gray-400">Master cutting-edge technology</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-red-500 mb-2">Business</div>
                    <p class="text-gray-400">Build profitable enterprises</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-red-500 mb-2">Mindset</div>
                    <p class="text-gray-400">Transform your thinking</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
                <div class="text-3xl md:text-4xl font-bold text-red-500">1000+</div>
                <div class="text-gray-400 mt-2">Students Transformed</div>
            </div>
            <div>
                <div class="text-3xl md:text-4xl font-bold text-red-500"><?php echo e($stats['courses']); ?>+</div>
                <div class="text-gray-400 mt-2">Expert Courses</div>
            </div>
            <div>
                <div class="text-3xl md:text-4xl font-bold text-red-500"><?php echo e($stats['success_rate']); ?>%</div>
                <div class="text-gray-400 mt-2">Success Rate</div>
            </div>
            <div>
                <div class="text-3xl md:text-4xl font-bold text-red-500"><?php echo e($stats['support_hours']); ?></div>
                <div class="text-gray-400 mt-2">Support Access</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Courses -->
<section class="py-20 bg-black">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold mb-6">
                Featured <span class="text-red-500">Transformations</span>
            </h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Our most popular courses that have helped thousands break free from limitations.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $featuredCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden hover:border-red-500 transition-all duration-300 group">
                    <div class="relative">
                        <img src="<?php echo e($course->image ? asset('storage/' . $course->image) : asset('images/course-template.svg')); ?>"
                             alt="<?php echo e($course->title); ?>"
                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 left-4">
                            <span class="bg-red-600 text-white px-2 py-1 rounded text-sm">
                                <?php if($course->category && is_object($course->category)): ?>
                                    <?php echo e($course->category->name); ?>

                                <?php elseif($course->category): ?>
                                    <?php echo e($course->category); ?>

                                <?php else: ?>
                                    General
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="absolute top-4 right-4">
                            <div class="bg-black/70 text-white px-2 py-1 rounded text-sm">
                            <?php if($course->hasDiscount()): ?>
                                <span class="line-through text-gray-400 text-xs">$<?php echo e(number_format($course->original_price, 2, '.', '')); ?></span>
                                <span class="text-red-400 ml-1 font-bold">$<?php echo e(number_format($course->price, 2, '.', '')); ?></span>
                            <?php elseif($course->isFree()): ?>
                                <span class="text-green-400 font-bold">Free</span>
                            <?php else: ?>
                                <span class="font-bold">$<?php echo e(number_format($course->price, 2, '.', '')); ?></span>
                            <?php endif; ?>
                        </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2 group-hover:text-red-500 transition-colors"><?php echo e($course->title); ?></h3>
                        <p class="text-gray-400 mb-4 line-clamp-2"><?php echo e($course->description); ?></p>

                        <div class="flex items-center gap-4 text-sm text-gray-500 mb-4">
                            <div class="flex items-center gap-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <?php echo e($course->level); ?>

                            </div>
                            <div class="flex items-center gap-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?php echo e($course->duration); ?>

                            </div>
                        </div>

                        <?php if($course->is_enrolled): ?>
                            <a href="<?php echo e(route('my-courses.view', $course)); ?>" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors inline-block text-center">
                                Continue Learning
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('courses.show', $course)); ?>" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors inline-block text-center">
                                Learn More
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-12">
            <a href="<?php echo e(route('courses.index')); ?>" class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-md text-lg font-semibold transition-colors inline-flex items-center">
                View All Courses
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-red-600 to-red-800">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">Ready to Escape the Matrix?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands who have already transformed their lives. Your journey to freedom starts now.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo e(route('courses.index')); ?>" class="bg-white text-black hover:bg-gray-100 px-8 py-3 rounded-md text-lg font-semibold transition-colors inline-flex items-center justify-center">
                Start Your Transformation
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
            </a>
            <a href="<?php echo e(route('contact')); ?>" class="border border-white text-white hover:bg-white hover:text-black px-8 py-3 rounded-md text-lg font-semibold transition-colors">
                Get Free Consultation
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/home.blade.php ENDPATH**/ ?>