@extends('layouts.app')

@section('title', $course->title . ' - Course Overview')

@push('styles')
<style>
body {
    background-color: #000;
}

/* Ensure buttons are visible and properly styled */
.chapter-item .flex.space-x-2 button,
.lecture-item .flex.space-x-2 button {
    min-width: 32px;
    min-height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.chapter-item .flex.space-x-2 button:hover,
.lecture-item .flex.space-x-2 button:hover {
    transform: scale(1.1);
}

/* Ensure FontAwesome icons are visible */
.fas {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

/* Save button styling */
#save-changes-btn {
    position: relative;
    z-index: 10;
}

/* Reorder button styling */
.move-chapter-up, .move-chapter-down,
.move-lecture-up, .move-lecture-down {
    padding: 0.5rem;
    background-color: rgba(55, 65, 81, 0.5);
    border: 1px solid rgba(75, 85, 99, 0.5);
}

.move-chapter-up:hover, .move-chapter-down:hover,
.move-lecture-up:hover, .move-lecture-down:hover {
    background-color: rgba(75, 85, 99, 0.8);
}

/* Edit button styling */
.edit-chapter, .edit-lecture {
    padding: 0.5rem;
    background-color: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.edit-chapter:hover, .edit-lecture:hover {
    background-color: rgba(59, 130, 246, 0.4);
}

/* Auto-save indicator styles */
.auto-save-indicator {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auto-save-indicator.bg-blue-600 {
    background-color: rgba(37, 99, 235, 0.9);
}

.auto-save-indicator.bg-green-600 {
    background-color: rgba(22, 163, 74, 0.9);
}

.auto-save-indicator.bg-red-600 {
    background-color: rgba(220, 38, 38, 0.9);
}

/* Spinner animation for saving indicator */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-black text-white">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('instructor.courses.index') }}"
                           class="text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-arrow-left text-lg"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-white">{{ $course->title }}</h1>
                            <div class="flex items-center space-x-4 mt-2">
                                <span class="px-3 py-1 text-sm font-medium rounded-full
                                    @if($course->status === 'published') bg-green-600 text-white
                                    @elseif($course->status === 'draft') bg-yellow-600 text-black
                                    @elseif($course->status === 'under_review') bg-blue-600 text-white
                                    @else bg-gray-600 text-white @endif">
                                    {{ ucfirst(str_replace('_', ' ', $course->status)) }}
                                </span>
                                @if($course->featured)
                                    <span class="px-3 py-1 text-sm font-medium rounded-full bg-purple-600 text-white">
                                        Featured
                                    </span>
                                @endif
                                <span class="text-sm text-gray-400">
                                    Last updated {{ $course->updated_at->diffForHumans() }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('instructor.course-builder.show', $course) }}"
                           class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors inline-flex items-center">
                            <i class="fas fa-edit mr-2"></i>Course Builder
                        </a>
                        <form action="{{ route('instructor.courses.toggle-status', $course) }}" method="POST" class="inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit"
                                    class="bg-{{ $course->status === 'published' ? 'yellow' : 'green' }}-600 hover:bg-{{ $course->status === 'published' ? 'yellow' : 'green' }}-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-{{ $course->status === 'published' ? 'eye-slash' : 'eye' }} mr-2"></i>
                                {{ $course->status === 'published' ? 'Unpublish' : 'Publish' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Course Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Course Image and Basic Info -->
            <div class="lg:col-span-1">
                <div class="bg-gray-900 border border-gray-700 rounded-lg p-6">
                    <div class="mb-6">
                        @if($course->image && $course->getImageUrl())
                            <img src="{{ $course->getImageUrl() }}"
                                 alt="{{ $course->title }}"
                                 class="w-full h-48 object-cover rounded-lg border border-gray-600">
                        @else
                            <div class="w-full h-48 bg-gray-800 rounded-lg border border-gray-600 flex items-center justify-center">
                                <i class="fas fa-book text-4xl text-gray-500"></i>
                            </div>
                        @endif
                    </div>

                    <div class="space-y-4">
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">Category</h3>
                            <p class="text-sm text-white">
                                {{ $course->category->name ?? $course->category }}
                                @if($course->subcategory)
                                    → {{ $course->subcategory->name ?? $course->subcategory }}
                                @endif
                            </p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-400">Level</h3>
                            <p class="text-sm text-white">{{ ucfirst(str_replace('_', ' ', $course->level)) }}</p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-400">Language</h3>
                            <p class="text-sm text-white">{{ $course->language }}</p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-400">Price</h3>
                            <div class="flex items-center">
                                @if($course->hasDiscount())
                                <span class="text-sm text-gray-400 line-through">${{ number_format($course->original_price, 2, '.', '') }}</span>
                                <span class="text-lg font-bold text-red-400 ml-2">${{ number_format($course->price, 2, '.', '') }}</span>
                                <span class="bg-red-500 text-white text-xs px-1 py-0.5 rounded ml-1">{{ $course->getDiscountPercentage() }}% OFF</span>
                            @elseif($course->isFree())
                                <span class="text-lg font-bold text-green-400">Free</span>
                            @else
                                <span class="text-lg font-bold text-white">${{ number_format($course->price, 2, '.', '') }}</span>
                            @endif
                            </div>
                        </div>

                        @if($course->promotional_video)
                            <div>
                                <h3 class="text-sm font-medium text-gray-400">Promotional Video</h3>
                                <a href="{{ $course->promotional_video }}" target="_blank"
                                   class="text-sm text-blue-400 hover:text-blue-300">
                                    View Video <i class="fas fa-external-link-alt ml-1"></i>
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="lg:col-span-2">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-gray-900 border border-gray-700 rounded-lg p-6 text-center">
                        <div class="text-2xl font-bold text-blue-400">{{ $stats['total_enrollments'] }}</div>
                        <div class="text-sm text-gray-400">Total Students</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-700 rounded-lg p-6 text-center">
                        <div class="text-2xl font-bold text-green-400">{{ $stats['active_enrollments'] }}</div>
                        <div class="text-sm text-gray-400">Active Students</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-700 rounded-lg p-6 text-center">
                        <div class="text-2xl font-bold text-purple-400">${{ number_format($stats['total_revenue'], 2) }}</div>
                        <div class="text-sm text-gray-400">Total Revenue</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-700 rounded-lg p-6 text-center">
                        <div class="text-2xl font-bold text-yellow-400">{{ number_format($stats['average_rating'], 1) }}</div>
                        <div class="text-sm text-gray-400">Average Rating</div>
                    </div>
                </div>

                <div class="bg-gray-900 border border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-white mb-4">Course Description</h3>
                    <p class="text-gray-300 whitespace-pre-line leading-relaxed">{{ $course->description }}</p>

                    @if($course->what_you_will_learn)
                        <div class="mt-6">
                            <h4 class="font-medium text-white mb-2">What you'll learn:</h4>
                            <ul class="list-disc list-inside space-y-1 text-sm text-gray-300">
                                @foreach($course->what_you_will_learn as $item)
                                    <li>{{ $item }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if($course->requirements)
                        <div class="mt-6">
                            <h4 class="font-medium text-white mb-2">Requirements:</h4>
                            <ul class="list-disc list-inside space-y-1 text-sm text-gray-300">
                                @foreach($course->requirements as $item)
                                    <li>{{ $item }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if($course->target_audience)
                        <div class="mt-6">
                            <h4 class="font-medium text-white mb-2">Target audience:</h4>
                            <ul class="list-disc list-inside space-y-1 text-sm text-gray-300">
                                @foreach($course->target_audience as $item)
                                    <li>{{ $item }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Course Details Edit Section -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-lg font-medium text-white">Course Details</h2>
                <button type="button" id="edit-course-details-btn"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-edit mr-2"></i>Edit Course Details
                </button>
            </div>

            <!-- Course Details Display Mode -->
            <div id="course-details-display" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-400 mb-1">Title</h3>
                        <p class="text-white font-medium">{{ $course->title }}</p>
                    </div>
                    @if($course->subtitle)
                    <div>
                        <h3 class="text-sm font-medium text-gray-400 mb-1">Subtitle</h3>
                        <p class="text-gray-300">{{ $course->subtitle }}</p>
                    </div>
                    @endif
                </div>

                <div>
                    <h3 class="text-sm font-medium text-gray-400 mb-1">Description</h3>
                    <p class="text-gray-300 whitespace-pre-line">{{ $course->description }}</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-400 mb-1">Category</h3>
                        <p class="text-gray-300">{{ $course->category->name ?? $course->category }}</p>
                        @if($course->subcategory)
                            <p class="text-sm text-gray-400">{{ $course->subcategory->name ?? $course->subcategory }}</p>
                        @endif
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-400 mb-1">Language & Level</h3>
                        <p class="text-gray-300">{{ $course->language }}</p>
                        <p class="text-sm text-gray-400">{{ ucfirst(str_replace('_', ' ', $course->level)) }}</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-400 mb-1">Pricing</h3>
                        @if($course->hasDiscount())
                            <p class="text-sm text-gray-400 line-through">${{ number_format($course->original_price, 2, '.', '') }}</p>
                            <p class="text-red-400 font-medium">${{ number_format($course->price, 2, '.', '') }}</p>
                            <span class="bg-red-500 text-white text-xs px-1 py-0.5 rounded">{{ $course->getDiscountPercentage() }}% OFF</span>
                        @elseif($course->isFree())
                            <p class="text-green-400 font-medium">Free</p>
                        @else
                            <p class="text-white font-medium">${{ number_format($course->price, 2, '.', '') }}</p>
                        @endif
                    </div>
                </div>

                @if($course->promotional_video)
                <div>
                    <h3 class="text-sm font-medium text-gray-400 mb-1">Promotional Video</h3>
                    <a href="{{ $course->promotional_video }}" target="_blank"
                       class="text-blue-400 hover:text-blue-300 text-sm">
                        {{ $course->promotional_video }} <i class="fas fa-external-link-alt ml-1"></i>
                    </a>
                </div>
                @endif
            </div>

            <!-- Course Details Edit Mode -->
            <div id="course-details-edit" class="hidden">
                <form id="course-details-form" method="POST" action="{{ route('instructor.courses.update-details', $course) }}" enctype="multipart/form-data" class="space-y-6">
                    @csrf
                    @method('PATCH')

                    <!-- Course Title & Subtitle -->
                    <div class="space-y-4">
                        <div>
                            <label for="edit_title" class="block text-sm font-medium text-gray-300">Course Title *</label>
                            <input type="text" name="title" id="edit_title"
                                   value="{{ old('title', $course->title) }}"
                                   class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 placeholder-gray-400 px-4 py-3"
                                   placeholder="e.g., Complete Web Development Bootcamp"
                                   required maxlength="255">
                        </div>

                        <div>
                            <label for="edit_subtitle" class="block text-sm font-medium text-gray-300">Course Subtitle</label>
                            <input type="text" name="subtitle" id="edit_subtitle"
                                   value="{{ old('subtitle', $course->subtitle) }}"
                                   class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 placeholder-gray-400 px-4 py-3"
                                   placeholder="e.g., Learn HTML, CSS, JavaScript, Node.js, React and more!"
                                   maxlength="255">
                        </div>

                        <div>
                            <label for="edit_description" class="block text-sm font-medium text-gray-300">Course Description *</label>
                            <textarea name="description" id="edit_description" rows="6"
                                      class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 placeholder-gray-400 px-4 py-3"
                                      placeholder="Describe what students will learn in this course..."
                                      required maxlength="5000">{{ old('description', $course->description) }}</textarea>
                        </div>
                    </div>

                    <!-- Category & Level -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit_category_id" class="block text-sm font-medium text-gray-300">Category *</label>
                            <select name="category_id" id="edit_category_id"
                                    class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                                    required>
                                <option value="">Select a category</option>
                                @foreach(\App\Models\CourseCategory::active()->parents()->with('activeChildren')->orderBy('name')->get() as $category)
                                    <option value="{{ $category->id }}"
                                            {{ $course->category_id == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="edit_subcategory_id" class="block text-sm font-medium text-gray-300">Subcategory</label>
                            <select name="subcategory_id" id="edit_subcategory_id"
                                    class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3">
                                <option value="">Select a subcategory</option>
                                @if($course->subcategory_id)
                                    <option value="{{ $course->subcategory_id }}" selected>
                                        {{ $course->subcategory->name ?? $course->subcategory }}
                                    </option>
                                @endif
                            </select>
                        </div>

                        <div>
                            <label for="edit_language" class="block text-sm font-medium text-gray-300">Language *</label>
                            <select name="language" id="edit_language"
                                    class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                                    required>
                                @foreach(['English', 'Spanish', 'French', 'German', 'Portuguese', 'Italian', 'Chinese', 'Japanese'] as $language)
                                    <option value="{{ $language }}"
                                            {{ $course->language == $language ? 'selected' : '' }}>
                                        {{ $language }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="edit_level" class="block text-sm font-medium text-gray-300">Level *</label>
                            <select name="level" id="edit_level"
                                    class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                                    required>
                                @foreach(['beginner', 'intermediate', 'advanced', 'all_levels'] as $level)
                                    <option value="{{ $level }}"
                                            {{ $course->level == $level ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('_', ' ', $level)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Access Type *</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="access_type" value="free"
                                           {{ $course->price == 0 ? 'checked' : '' }}
                                           class="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-600 bg-gray-900">
                                    <span class="ml-2 text-sm text-gray-300">Free</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="access_type" value="paid"
                                           {{ $course->price > 0 ? 'checked' : '' }}
                                           class="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-600 bg-gray-900">
                                    <span class="ml-2 text-sm text-gray-300">Paid</span>
                                </label>
                            </div>
                        </div>

                        <div id="edit-pricing-fields" class="grid grid-cols-1 md:grid-cols-2 gap-4" style="{{ $course->price > 0 ? '' : 'display: none;' }}">
                            <div>
                                <label for="edit_price" class="block text-sm font-medium text-gray-300">Price (USD) *</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-400 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" name="price" id="edit_price"
                                           value="{{ $course->price }}"
                                           class="pl-7 pr-4 py-3 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 placeholder-gray-400"
                                           placeholder="0.00" step="0.01" min="0" max="999.99">
                                </div>
                            </div>

                            <div>
                                <label for="edit_original_price" class="block text-sm font-medium text-gray-300">Original Price (for discounts)</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-400 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" name="original_price" id="edit_original_price"
                                           value="{{ $course->original_price }}"
                                           class="pl-7 pr-4 py-3 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 placeholder-gray-400"
                                           placeholder="0.00" step="0.01" min="0" max="999.99">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Course Media -->
                    <div class="space-y-4">
                        <div>
                            <label for="edit_image" class="block text-sm font-medium text-gray-300">Course Thumbnail</label>
                            @if($course->image && $course->getImageUrl())
                                <div class="mt-2 mb-4">
                                    <img src="{{ $course->getImageUrl() }}"
                                         alt="Current thumbnail"
                                         class="w-32 h-20 object-cover rounded border border-gray-600">
                                    <p class="text-xs text-gray-400 mt-1">Current thumbnail</p>
                                </div>
                            @endif
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-md bg-gray-900">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-400">
                                        <label for="edit_image" class="relative cursor-pointer bg-gray-800 rounded-md font-medium text-red-500 hover:text-red-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-red-500">
                                            <span>Upload a file</span>
                                            <input id="edit_image" name="image" type="file" class="sr-only" accept="image/*">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="edit_promotional_video" class="block text-sm font-medium text-gray-300">Promotional Video URL</label>
                            <input type="url" name="promotional_video" id="edit_promotional_video"
                                   value="{{ $course->promotional_video }}"
                                   class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 placeholder-gray-400 px-4 py-3"
                                   placeholder="https://youtube.com/watch?v=...">
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                        <button type="button" id="cancel-course-details-edit"
                                class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors">
                            Cancel
                        </button>
                        <button type="submit" id="save-course-details-btn"
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                            <i class="fas fa-save mr-2"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Course Management Instructions -->
        <div class="bg-yellow-900/20 border border-yellow-800/30 rounded-lg p-4 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-lightbulb text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-300">Course Structure Management</h3>
                    <p class="mt-1 text-sm text-yellow-200">
                        Use the interface below to manage your course structure. You can add new chapters with lectures,
                        reorder content by dragging, and edit existing chapters. Changes are saved automatically.
                    </p>
                </div>
            </div>
        </div>

        <!-- Custom Confirmation Modal -->
        <div id="confirmation-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400 text-2xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-white" id="modal-title">Confirm Action</h3>
                    </div>
                </div>
                <div class="mb-6">
                    <p class="text-gray-300" id="modal-message">Are you sure you want to proceed?</p>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="modal-cancel" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors">
                        Cancel
                    </button>
                    <button type="button" id="modal-confirm" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        Confirm
                    </button>
                </div>
            </div>
        </div>

        <!-- Course Structure Management -->
        <form id="course-structure-form" method="POST" action="{{ route('instructor.courses.add-chapter', $course) }}" enctype="multipart/form-data">
            @csrf
            <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-lg font-medium text-white">Course Structure</h2>
                    <div class="flex space-x-3">
                        <button type="submit" id="save-changes-btn"
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                            <i class="fas fa-save mr-2"></i>Save Changes
                        </button>
                        <button type="button" id="add-chapter-btn"
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-plus mr-2"></i>Add Chapter
                        </button>
                    </div>
                </div>

                <div id="chapters-container" class="space-y-6">
                    @foreach($course->chapters as $index => $chapter)
                        <div class="chapter-item border border-gray-600 bg-gray-900 rounded-lg p-6" data-chapter-id="{{ $chapter->id }}" data-chapter-index="{{ $index }}">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-white">
                                    Chapter <span class="chapter-number">{{ $index + 1 }}</span>
                                </h3>
                                <div class="flex space-x-2">
                                    <button type="button" class="move-chapter-up text-gray-400 hover:text-gray-300" title="Move Up">
                                        <i class="fas fa-chevron-up"></i>
                                    </button>
                                    <button type="button" class="move-chapter-down text-gray-400 hover:text-gray-300" title="Move Down">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                    <button type="button" class="toggle-chapter-edit text-blue-400 hover:text-blue-300" title="Edit Chapter" data-chapter-id="{{ $chapter->id }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="edit-chapter text-blue-400 hover:text-blue-300" title="Edit in Separate Page" data-chapter-id="{{ $chapter->id }}">
                                        <i class="fas fa-external-link-alt"></i>
                                    </button>
                                    @if($chapter->lectures->count() === 0)
                                        <button type="button" class="remove-chapter text-red-400 hover:text-red-300" title="Remove Chapter" data-chapter-id="{{ $chapter->id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>

                            <div class="space-y-4">
                                <!-- Chapter Display Mode -->
                                <div class="chapter-display grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h4 class="font-medium text-white chapter-title-display">{{ $chapter->title }}</h4>
                                        @if($chapter->description)
                                            <p class="text-sm text-gray-300 mt-1 chapter-description-display">{{ $chapter->description }}</p>
                                        @endif
                                    </div>
                                    <div class="flex items-center space-x-4 text-xs text-gray-400">
                                        <span>{{ $chapter->lectures->count() }} lectures</span>
                                        <span>{{ $chapter->getFormattedDuration() }}</span>
                                        <span class="px-2 py-1 rounded-full {{ $chapter->is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                            {{ $chapter->is_published ? 'Published' : 'Draft' }}
                                        </span>
                                        @if($chapter->is_free_preview)
                                            <span class="px-2 py-1 rounded-full bg-blue-100 text-blue-800">Free Preview</span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Chapter Edit Mode -->
                                <div class="chapter-edit hidden">
                                    <div class="chapter-edit-form" data-chapter-id="{{ $chapter->id }}">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-300 mb-2">Chapter Title <span class="text-red-500">*</span></label>
                                                <input type="text" name="title" value="{{ $chapter->title }}"
                                                       class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                       required>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-300 mb-2">Publication Status</label>
                                                <select name="is_published" class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                                    <option value="0" {{ !$chapter->is_published ? 'selected' : '' }}>Draft</option>
                                                    <option value="1" {{ $chapter->is_published ? 'selected' : '' }}>Published</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="mt-4">
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Chapter Description</label>
                                            <textarea name="description" rows="3"
                                                      class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                      placeholder="Describe what students will learn in this chapter">{{ $chapter->description }}</textarea>
                                        </div>
                                        <div class="mt-4">
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Learning Objectives</label>
                                            <textarea name="learning_objectives" rows="3"
                                                      class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                      placeholder="What will students learn in this chapter? (one per line)">{{ is_array($chapter->learning_objectives) ? implode("\n", $chapter->learning_objectives) : $chapter->learning_objectives }}</textarea>
                                            <p class="mt-1 text-xs text-gray-400">Enter each objective on a new line</p>
                                        </div>
                                        <div class="mt-4">
                                            <label class="flex items-center">
                                                <input type="checkbox" name="is_free_preview" value="1" {{ $chapter->is_free_preview ? 'checked' : '' }}
                                                       class="text-red-500 focus:ring-red-500 focus:ring-2">
                                                <span class="ml-2 text-gray-300">Allow free preview of this chapter</span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-700">
                                            <button type="button" class="cancel-chapter-edit px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors" data-chapter-id="{{ $chapter->id }}">
                                                Cancel
                                            </button>
                                            <div class="auto-save-status px-4 py-2 text-green-400 text-sm font-medium">
                                                <i class="fas fa-cloud mr-2"></i>Auto-save enabled
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="border-t border-gray-600 pt-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-medium text-white">Lectures</h4>
                                        <button type="button" class="add-lecture-btn bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors" data-chapter-id="{{ $chapter->id }}">
                                            <i class="fas fa-plus mr-1"></i>Add Lecture
                                        </button>
                                    </div>

                                    <div class="lectures-container space-y-3">
                                        @foreach($chapter->lectures as $lectureIndex => $lecture)
                                            <div class="lecture-item bg-gray-800 border border-gray-600 rounded-lg p-4" data-lecture-id="{{ $lecture->id }}" data-lecture-index="{{ $lectureIndex }}">
                                                <div class="flex items-center justify-between mb-3">
                                                    <h5 class="font-medium text-gray-300">
                                                        Lecture <span class="lecture-number">{{ $lectureIndex + 1 }}</span>
                                                    </h5>
                                                    <div class="flex space-x-2">
                                                        <button type="button" class="move-lecture-up text-gray-400 hover:text-gray-300" title="Move Up">
                                                            <i class="fas fa-chevron-up"></i>
                                                        </button>
                                                        <button type="button" class="move-lecture-down text-gray-400 hover:text-gray-300" title="Move Down">
                                                            <i class="fas fa-chevron-down"></i>
                                                        </button>
                                                        <button type="button" class="toggle-lecture-edit text-blue-400 hover:text-blue-300" title="Edit Lecture" data-lecture-id="{{ $lecture->id }}" data-chapter-id="{{ $chapter->id }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="edit-lecture text-blue-400 hover:text-blue-300" title="Edit in Separate Page" data-lecture-id="{{ $lecture->id }}">
                                                            <i class="fas fa-external-link-alt"></i>
                                                        </button>
                                                        <button type="button" class="remove-lecture text-red-400 hover:text-red-300" title="Remove Lecture" data-lecture-id="{{ $lecture->id }}">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Lecture Display Mode -->
                                                <div class="lecture-display grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <h4 class="font-medium text-white lecture-title-display">{{ $lecture->title }}</h4>
                                                        @if($lecture->description)
                                                            <p class="text-sm text-gray-400 mt-1 lecture-description-display">{{ Str::limit($lecture->description, 100) }}</p>
                                                        @endif
                                                    </div>
                                                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                                                        <span class="px-2 py-1 rounded-full
                                                            @if($lecture->type === 'video') bg-red-100 text-red-800
                                                            @elseif($lecture->type === 'text') bg-blue-100 text-blue-800
                                                            @elseif($lecture->type === 'quiz') bg-green-100 text-green-800
                                                            @elseif($lecture->type === 'assignment') bg-purple-100 text-purple-800
                                                            @else bg-gray-100 text-gray-800 @endif">
                                                            {{ ucfirst($lecture->type) }}
                                                        </span>
                                                        @if($lecture->duration_minutes)
                                                            <span><i class="fas fa-clock mr-1"></i>{{ $lecture->duration_minutes }} min</span>
                                                        @endif
                                                        <span class="px-2 py-1 rounded-full {{ $lecture->is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                            {{ $lecture->is_published ? 'Published' : 'Draft' }}
                                                        </span>
                                                        @if($lecture->is_free_preview)
                                                            <span class="px-2 py-1 rounded-full bg-blue-100 text-blue-800">Free Preview</span>
                                                        @endif
                                                        @if($lecture->is_mandatory)
                                                            <span class="px-2 py-1 rounded-full bg-orange-100 text-orange-800">Mandatory</span>
                                                        @endif
                                                    </div>
                                                </div>

                                                <!-- Lecture Edit Mode -->
                                                <div class="lecture-edit hidden">
                                                    <div class="lecture-edit-form" data-lecture-id="{{ $lecture->id }}" data-chapter-id="{{ $chapter->id }}">
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-300 mb-2">Lecture Title <span class="text-red-500">*</span></label>
                                                                <input type="text" name="title" value="{{ $lecture->title }}"
                                                                       class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                                       required>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-300 mb-2">Lecture Type <span class="text-red-500">*</span></label>
                                                                <select name="type" class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent" required>
                                                                    <option value="video" {{ $lecture->type === 'video' ? 'selected' : '' }}>Video Lecture</option>
                                                                    <option value="text" {{ $lecture->type === 'text' ? 'selected' : '' }}>Text Content</option>
                                                                    <option value="quiz" {{ $lecture->type === 'quiz' ? 'selected' : '' }}>Quiz</option>
                                                                    <option value="assignment" {{ $lecture->type === 'assignment' ? 'selected' : '' }}>Assignment</option>
                                                                    <option value="resource" {{ $lecture->type === 'resource' ? 'selected' : '' }}>Resource/Download</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="mt-4">
                                                            <label class="block text-sm font-medium text-gray-300 mb-2">Lecture Description</label>
                                                            <textarea name="description" rows="3"
                                                                      class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                                      placeholder="Brief description of this lecture">{{ $lecture->description }}</textarea>
                                                        </div>

                                                        <!-- Dynamic Content Based on Type -->
                                                        <div class="lecture-content-sections mt-4">
                                                            <!-- Video Content -->
                                                            <div class="content-section video-content" style="display: {{ $lecture->type === 'video' ? 'block' : 'none' }}">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-300 mb-2">Video URL <span class="text-red-500">*</span></label>
                                                                    <input type="url" name="video_url" value="{{ $lecture->video_url }}"
                                                                           class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                                           placeholder="https://youtube.com/watch?v=... or direct video URL">
                                                                </div>
                                                            </div>

                                                            <!-- Text/Assignment Content -->
                                                            <div class="content-section text-content" style="display: {{ in_array($lecture->type, ['text', 'assignment']) ? 'block' : 'none' }}">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-300 mb-2">Content</label>
                                                                    <textarea name="content" rows="6"
                                                                              class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                                              placeholder="Enter your content here...">{{ $lecture->content }}</textarea>
                                                                </div>
                                                            </div>

                                                            <!-- Quiz Content -->
                                                            <div class="content-section quiz-content" style="display: {{ $lecture->type === 'quiz' ? 'block' : 'none' }}">
                                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                    <div>
                                                                        <label class="block text-sm font-medium text-gray-300 mb-2">Passing Score (%)</label>
                                                                        <input type="number" name="quiz_passing_score" value="{{ $lecture->quiz_passing_score ?? 70 }}" min="0" max="100"
                                                                               class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                                                    </div>
                                                                    <div class="flex items-center pt-6">
                                                                        <label class="flex items-center">
                                                                            <input type="checkbox" name="quiz_allow_retakes" value="1" {{ $lecture->quiz_allow_retakes ? 'checked' : '' }}
                                                                                   class="text-red-500 focus:ring-red-500 focus:ring-2">
                                                                            <span class="ml-2 text-gray-300">Allow retakes</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <p class="text-sm text-gray-400 mt-2">Quiz questions can be managed after saving the lecture.</p>
                                                            </div>

                                                            <!-- Resource Content -->
                                                            <div class="content-section resource-content" style="display: {{ $lecture->type === 'resource' ? 'block' : 'none' }}">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-300 mb-2">Resource Description</label>
                                                                    <textarea name="resource_description" rows="3"
                                                                              class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                                              placeholder="Describe what this resource contains and how students should use it...">{{ $lecture->resource_description }}</textarea>
                                                                </div>
                                                                <p class="text-sm text-gray-400 mt-2">Resource files can be managed after saving the lecture.</p>
                                                            </div>
                                                        </div>
                                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                                                    Duration (minutes)
                                                                    @if($lecture->type === 'video')
                                                                        <span class="text-red-500">*</span>
                                                                    @endif
                                                                </label>
                                                                <input type="number" name="duration_minutes" value="{{ $lecture->duration_minutes }}"
                                                                       min="{{ $lecture->type === 'video' ? '1' : '0' }}"
                                                                       class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                                                       placeholder="{{ $lecture->type === 'video' ? 'e.g., 15' : 'Estimated time (optional)' }}">
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-300 mb-2">Publication Status</label>
                                                                <select name="is_published" class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                                                    <option value="0" {{ !$lecture->is_published ? 'selected' : '' }}>Draft</option>
                                                                    <option value="1" {{ $lecture->is_published ? 'selected' : '' }}>Published</option>
                                                                </select>
                                                            </div>
                                                            <div class="flex items-center space-x-4 pt-6">
                                                                <label class="flex items-center">
                                                                    <input type="checkbox" name="is_free_preview" value="1" {{ $lecture->is_free_preview ? 'checked' : '' }}
                                                                           class="text-red-500 focus:ring-red-500 focus:ring-2">
                                                                    <span class="ml-2 text-gray-300 text-sm">Free Preview</span>
                                                                </label>
                                                                <label class="flex items-center">
                                                                    <input type="checkbox" name="is_mandatory" value="1" {{ $lecture->is_mandatory ? 'checked' : '' }}
                                                                           class="text-red-500 focus:ring-red-500 focus:ring-2">
                                                                    <span class="ml-2 text-gray-300 text-sm">Mandatory</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-700">
                                                            <button type="button" class="cancel-lecture-edit px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors" data-lecture-id="{{ $lecture->id }}">
                                                                Cancel
                                                            </button>
                                                            <div class="auto-save-status px-4 py-2 text-green-400 text-sm font-medium">
                                                                <i class="fas fa-cloud mr-2"></i>Auto-save enabled
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                @if($course->chapters->count() === 0)
                    <div class="text-center py-12" id="empty-state">
                        <i class="fas fa-book-open text-6xl text-gray-600 mb-6"></i>
                        <h3 class="text-xl font-medium text-gray-300 mb-4">No chapters yet</h3>
                        <p class="text-gray-500 mb-6">Start building your course by adding your first chapter.</p>
                        <button type="button" id="add-first-chapter-btn"
                                class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-plus mr-2"></i>Add First Chapter
                        </button>
                    </div>
                @endif

                @if($errors->any())
                    <div class="mt-4 p-4 bg-red-900/20 border border-red-800/30 rounded-lg">
                        <h4 class="text-sm font-medium text-red-400 mb-2">Please fix the following errors:</h4>
                        <ul class="text-sm text-red-300 list-disc list-inside space-y-1">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        </form>
    </div>
</div>

<!-- Chapter Template -->
<template id="chapter-template">
    <div class="chapter-item border border-gray-600 bg-gray-900 rounded-lg p-6" data-chapter-index="">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-white">
                Chapter <span class="chapter-number"></span>
            </h3>
            <div class="flex space-x-2">
                <button type="button" class="move-chapter-up text-gray-400 hover:text-gray-300" title="Move Up">
                    <i class="fas fa-chevron-up"></i>
                </button>
                <button type="button" class="move-chapter-down text-gray-400 hover:text-gray-300" title="Move Down">
                    <i class="fas fa-chevron-down"></i>
                </button>
                <button type="button" class="remove-chapter text-red-400 hover:text-red-300" title="Remove Chapter">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-300">Chapter Title *</label>
                <input type="text" name="chapters[INDEX][title]"
                       class="mt-1 block w-full bg-gray-800 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 chapter-title px-4 py-3"
                       placeholder="e.g., Introduction to Web Development" required maxlength="255">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300">Chapter Description</label>
                <textarea name="chapters[INDEX][description]" rows="3"
                          class="mt-1 block w-full bg-gray-800 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                          placeholder="Brief description of what this chapter covers..." maxlength="1000"></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300">Learning Objectives</label>
                    <textarea name="chapters[INDEX][learning_objectives]" rows="3"
                              class="mt-1 block w-full bg-gray-800 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                              placeholder="What will students learn in this chapter? (one per line)"></textarea>
                    <p class="mt-1 text-xs text-gray-400">Enter each objective on a new line</p>
                </div>
                <div>
                    <label class="flex items-center mt-6">
                        <input type="checkbox" name="chapters[INDEX][is_free_preview]" value="1"
                               class="rounded border-gray-600 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 bg-gray-700">
                        <span class="ml-2 text-sm text-gray-300">Free Preview Chapter</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-400">Allow non-enrolled students to access this chapter</p>
                </div>
            </div>

            <div class="border-t border-gray-600 pt-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-white">Lectures</h4>
                    <button type="button" class="add-lecture-btn bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors">
                        <i class="fas fa-plus mr-1"></i>Add Lecture
                    </button>
                </div>

                <div class="lectures-container space-y-3">
                    <!-- Lectures will be added here -->
                </div>
            </div>

            <!-- Save and Cancel buttons for new chapters -->
            <div class="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-700">
                <button type="button" class="cancel-new-chapter px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors">
                    Cancel
                </button>
                <button type="button" class="save-new-chapter px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                    <i class="fas fa-save mr-2"></i>Save Chapter
                </button>
            </div>
        </div>
    </div>
</template>

<!-- Lecture Template -->
<template id="lecture-template">
    <div class="lecture-item bg-gray-800 border border-gray-600 rounded-lg p-4" data-lecture-index="">
        <div class="flex items-center justify-between mb-3">
            <h5 class="font-medium text-gray-300">
                Lecture <span class="lecture-number"></span>
            </h5>
            <div class="flex space-x-2">
                <button type="button" class="move-lecture-up text-gray-400 hover:text-gray-300" title="Move Up">
                    <i class="fas fa-chevron-up"></i>
                </button>
                <button type="button" class="move-lecture-down text-gray-400 hover:text-gray-300" title="Move Down">
                    <i class="fas fa-chevron-down"></i>
                </button>
                <button type="button" class="remove-lecture text-red-400 hover:text-red-300" title="Remove Lecture">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-300">Lecture Title *</label>
                <input type="text" name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][title]"
                       class="mt-1 block w-full bg-gray-700 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 lecture-title px-4 py-3"
                       placeholder="e.g., Setting up your development environment" required maxlength="255">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300">Lecture Type *</label>
                <select name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][type]"
                        class="mt-1 block w-full bg-gray-700 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 lecture-type px-4 py-3" required>
                    <option value="">Select type</option>
                    <option value="video">Video Lecture</option>
                    <option value="text">Text Content</option>
                    <option value="quiz">Quiz</option>
                    <option value="assignment">Assignment</option>
                    <option value="resource">Resource/Download</option>
                </select>
            </div>
        </div>

        <div class="mt-3">
            <label class="block text-sm font-medium text-gray-300">Lecture Description</label>
            <textarea name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][description]" rows="2"
                      class="mt-1 block w-full bg-gray-700 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                      placeholder="Brief description of this lecture..." maxlength="1000"></textarea>
        </div>

        <!-- Content Creation Section (appears when lecture type is selected) -->
        <div class="lecture-content-section mt-4 pt-4 border-t border-gray-600" style="display: none;">
            <h6 class="text-sm font-medium text-gray-300 mb-3">Lecture Content</h6>

            <!-- Video Content Form -->
            <div class="content-form video-content" style="display: none;">
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-300">Video URL</label>
                        <input type="url" name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][video_url]"
                               class="mt-1 block w-full bg-gray-700 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                               placeholder="https://youtube.com/watch?v=... or https://vimeo.com/...">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300">Duration (minutes)</label>
                        <input type="number" name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][duration_minutes]"
                               class="mt-1 block w-full bg-gray-700 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                               placeholder="e.g., 15" min="1" max="300">
                    </div>
                </div>
            </div>

            <!-- Text Content Form -->
            <div class="content-form text-content" style="display: none;">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Lesson Content</label>
                        <textarea name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][content]" rows="6"
                                  class="mt-1 block w-full bg-gray-700 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                                  placeholder="Write your lesson content here..."></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300">Estimated Reading Time (minutes)</label>
                            <input type="number" name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][estimated_completion_minutes]"
                                   class="mt-1 block w-full bg-gray-700 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 px-4 py-3"
                                   placeholder="5" min="1" max="120">
                        </div>
                        <div>
                            <label class="flex items-center mt-6">
                                <input type="checkbox" name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][allow_comments]" value="1" checked
                                       class="rounded bg-gray-700 border-gray-600 text-red-600 focus:ring-red-500 focus:ring-offset-gray-800">
                                <span class="ml-2 text-sm text-gray-300">Allow student comments</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Common fields for all lecture types -->
            <div class="mt-4 pt-4 border-t border-gray-600">
                <div class="flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][is_free_preview]" value="1"
                               class="rounded border-gray-600 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 bg-gray-700">
                        <span class="ml-2 text-sm text-gray-300">Free Preview</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="chapters[CHAPTER_INDEX][lectures][LECTURE_INDEX][is_mandatory]" value="1" checked
                               class="rounded border-gray-600 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 bg-gray-700">
                        <span class="ml-2 text-sm text-gray-300">Mandatory</span>
                    </label>
                </div>
            </div>

            <!-- Save and Cancel buttons for new lectures -->
            <div class="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-700">
                <button type="button" class="cancel-new-lecture px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors">
                    Cancel
                </button>
                <button type="button" class="save-new-lecture px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                    <i class="fas fa-save mr-2"></i>Save Lecture
                </button>
            </div>
        </div>
    </div>
</template>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Course management will be handled by the new course builder
    console.log('Course show page loaded - redirecting to course builder for editing');
});
























});
</script>
@endpush
