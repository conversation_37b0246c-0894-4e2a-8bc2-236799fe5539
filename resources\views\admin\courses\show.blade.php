@extends('layouts.app')

@section('title', 'Course Details - ' . $course->title)

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                    <div>
                        <div class="flex items-center space-x-3 mb-2">
                            <a href="{{ route('admin.courses.index') }}"
                               class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <h1 class="text-2xl md:text-3xl font-bold text-white">Course <span class="text-red-500">Details</span></h1>
                        </div>
                        <p class="text-base md:text-lg text-gray-400">Review and moderate course content</p>
                    </div>

                    <div class="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3">
                        <a href="{{ route('admin.courses.content', $course) }}"
                           class="bg-red-600 hover:bg-red-700 text-white px-4 py-3 md:px-6 md:py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center gap-2 text-sm md:text-base">
                            <i class="fas fa-search"></i>
                            <span>Review Content</span>
                        </a>
                        <a href="{{ route('courses.show', $course) }}"
                           target="_blank"
                           class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-3 md:px-6 md:py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center gap-2 border border-gray-600 text-sm md:text-base">
                            <i class="fas fa-external-link-alt"></i>
                            <span>View Public Page</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Course Overview -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">Course Overview</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <h3 class="text-lg font-medium text-white mb-2">{{ $course->title }}</h3>
                            @if($course->subtitle)
                                <p class="text-gray-400">{{ $course->subtitle }}</p>
                            @endif
                        </div>

                        @if($course->description)
                            <div>
                                <h4 class="text-sm font-medium text-gray-300 mb-2">Description</h4>
                                <div class="text-gray-400 prose prose-invert max-w-none">
                                    {!! nl2br(e($course->description)) !!}
                                </div>
                            </div>
                        @endif

                        @if($course->what_you_will_learn)
                            <div>
                                <h4 class="text-sm font-medium text-gray-300 mb-2">What You'll Learn</h4>
                                <div class="text-gray-400 prose prose-invert max-w-none">
                                    {!! nl2br(e($course->what_you_will_learn)) !!}
                                </div>
                            </div>
                        @endif

                        @if($course->requirements)
                            <div>
                                <h4 class="text-sm font-medium text-gray-300 mb-2">Requirements</h4>
                                <div class="text-gray-400 prose prose-invert max-w-none">
                                    {!! nl2br(e($course->requirements)) !!}
                                </div>
                            </div>
                        @endif

                        @if($course->target_audience)
                            <div>
                                <h4 class="text-sm font-medium text-gray-300 mb-2">Target Audience</h4>
                                <div class="text-gray-400 prose prose-invert max-w-none">
                                    {!! nl2br(e($course->target_audience)) !!}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Course Structure -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">Course Structure</h2>
                    
                    @if($course->chapters->count() > 0)
                        <div class="space-y-4">
                            @foreach($course->chapters as $chapter)
                                <div class="border border-gray-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <h3 class="text-lg font-medium text-white">{{ $chapter->title }}</h3>
                                        <span class="text-sm text-gray-400">{{ $chapter->lectures->count() }} lectures</span>
                                    </div>
                                    
                                    @if($chapter->description)
                                        <p class="text-gray-400 mb-3">{{ $chapter->description }}</p>
                                    @endif

                                    @if($chapter->lectures->count() > 0)
                                        <div class="space-y-2">
                                            @foreach($chapter->lectures as $lecture)
                                                <div class="flex items-center justify-between bg-gray-900 p-3 rounded">
                                                    <div class="flex items-center space-x-3">
                                                        <i class="fas fa-play-circle text-red-500"></i>
                                                        <span class="text-white">{{ $lecture->title }}</span>
                                                    </div>
                                                    <div class="flex items-center space-x-4 text-sm text-gray-400">
                                                        @if($lecture->duration)
                                                            <span>{{ gmdate('H:i:s', $lecture->duration) }}</span>
                                                        @endif
                                                        <span class="px-2 py-1 bg-gray-700 rounded text-xs">
                                                            {{ ucfirst($lecture->type) }}
                                                        </span>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <p class="text-gray-500 italic">No lectures in this chapter</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 italic">No chapters created yet</p>
                    @endif
                </div>

                <!-- Reviews -->
                @if($course->reviews->count() > 0)
                    <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6">
                        <h2 class="text-xl font-semibold text-white mb-4">Recent Reviews</h2>
                        
                        <div class="space-y-4">
                            @foreach($course->reviews->take(5) as $review)
                                <div class="border border-gray-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                                                <span class="text-white text-sm font-bold">{{ substr($review->user->name, 0, 1) }}</span>
                                            </div>
                                            <div>
                                                <p class="text-white font-medium">{{ $review->user->name }}</p>
                                                <div class="flex items-center">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star text-sm {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-600' }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                        </div>
                                        <span class="text-sm text-gray-400">{{ $review->created_at->diffForHumans() }}</span>
                                    </div>
                                    @if($review->comment)
                                        <p class="text-gray-400">{{ $review->comment }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Course Image -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Course Image</h3>
                    <div class="aspect-video bg-gray-900 rounded-lg overflow-hidden">
                        @if($course->image)
                            <img src="{{ Storage::disk('private')->url($course->image) }}"
                                 alt="{{ $course->title }}"
                                 class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <i class="fas fa-image text-4xl text-gray-500"></i>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Course Info -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Course Information</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Status</span>
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                @if($course->status === 'published') bg-green-600 text-green-100
                                @elseif($course->status === 'draft') bg-yellow-600 text-yellow-100
                                @elseif($course->status === 'under_review') bg-blue-600 text-blue-100
                                @elseif($course->status === 'suspended') bg-red-600 text-red-100
                                @else bg-gray-600 text-gray-100 @endif">
                                {{ ucfirst(str_replace('_', ' ', $course->status)) }}
                            </span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Featured</span>
                            <span class="text-white">{{ $course->featured ? 'Yes' : 'No' }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Category</span>
                            <span class="text-white">{{ $course->category->name ?? 'N/A' }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Level</span>
                            <span class="text-white">{{ ucfirst($course->level) }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Price</span>
                            <span class="text-white">
                                @if($course->price > 0)
                                    ${{ number_format($course->price, 2, '.', '') }}
                                @else
                                    Free
                                @endif
                            </span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Students</span>
                            <span class="text-white">{{ number_format($course->enrollments_count) }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Chapters</span>
                            <span class="text-white">{{ $course->chapters_count }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Lectures</span>
                            <span class="text-white">{{ $course->lectures_count }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Rating</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-white">{{ number_format($course->reviews_avg_rating ?: 0, 1) }}</span>
                                <div class="flex">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star text-xs {{ $i <= ($course->reviews_avg_rating ?: 0) ? 'text-yellow-400' : 'text-gray-600' }}"></i>
                                    @endfor
                                </div>
                                <span class="text-gray-400 text-sm">({{ $course->reviews_count }})</span>
                            </div>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Created</span>
                            <span class="text-white">{{ $course->created_at->format('M d, Y') }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-400">Updated</span>
                            <span class="text-white">{{ $course->updated_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Instructor Info -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Instructor</h3>
                    
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-lg font-bold">{{ substr($course->instructor->name, 0, 1) }}</span>
                        </div>
                        <div>
                            <p class="text-white font-medium">{{ $course->instructor->name }}</p>
                            <p class="text-gray-400 text-sm">{{ $course->instructor->email }}</p>
                        </div>
                    </div>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Total Courses</span>
                            <span class="text-white">{{ $course->instructor->courses_count }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Total Students</span>
                            <span class="text-white">{{ number_format($course->instructor->total_students ?? 0) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Member Since</span>
                            <span class="text-white">{{ $course->instructor->created_at->format('M Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Admin Actions -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Admin Actions</h3>
                    
                    <div class="space-y-3">
                        <form action="{{ route('admin.courses.update-status', $course) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <div class="mb-3">
                                <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Change Status</label>
                                <select name="status" id="status" class="w-full bg-gray-900 border-gray-600 text-white rounded-md">
                                    <option value="draft" {{ $course->status === 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="under_review" {{ $course->status === 'under_review' ? 'selected' : '' }}>Under Review</option>
                                    <option value="published" {{ $course->status === 'published' ? 'selected' : '' }}>Published</option>
                                    <option value="suspended" {{ $course->status === 'suspended' ? 'selected' : '' }}>Suspended</option>
                                </select>
                            </div>
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                Update Status
                            </button>
                        </form>

                        <form action="{{ route('admin.courses.toggle-featured', $course) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="w-full {{ $course->featured ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-gray-600 hover:bg-gray-700' }} text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                {{ $course->featured ? 'Remove from Featured' : 'Mark as Featured' }}
                            </button>
                        </form>

                        <a href="{{ route('admin.courses.content', $course) }}"
                           class="block w-full bg-red-600 hover:bg-red-700 text-white text-center px-4 py-2 rounded-lg font-medium transition-colors">
                            Review Course Content
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection