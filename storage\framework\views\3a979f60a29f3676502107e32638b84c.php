<?php $__env->startSection('title', 'My Courses'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <!-- Mobile-responsive header layout -->
                <div class="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                    <div>
                        <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-white">My <span class="text-red-500">Courses</span></h1>
                        <p class="mt-2 text-base md:text-lg text-gray-400">Manage and organize your course content</p>
                    </div>

                    <!-- Mobile: Stack buttons vertically, Desktop: Side by side -->
                    <div class="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3">
                        <button onclick="showCreateCourseModal()"
                           class="bg-red-600 hover:bg-red-700 text-white px-4 py-3 md:px-6 md:py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center gap-2 text-sm md:text-base">
                            <i class="fas fa-plus"></i>
                            <span class="hidden sm:inline">Create New Course</span>
                            <span class="sm:hidden">Create Course</span>
                        </button>
                        <a href="<?php echo e(route('instructor.dashboard')); ?>"
                           class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-3 md:px-6 md:py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center gap-2 border border-gray-600 text-sm md:text-base">
                            <i class="fas fa-chart-bar"></i>
                            <span>Dashboard</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Filters -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6 mb-6">
            <form method="GET" action="<?php echo e(route('instructor.courses.index')); ?>" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-300">Search</label>
                        <input type="text" name="search" id="search"
                               value="<?php echo e(request('search')); ?>"
                               class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 placeholder-gray-400"
                               placeholder="Search courses...">
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-300">Status</label>
                        <select name="status" id="status"
                                class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                            <option value="">All Statuses</option>
                            <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($status); ?>" <?php echo e(request('status') === $status ? 'selected' : ''); ?>>
                                    <?php echo e(ucfirst(str_replace('_', ' ', $status))); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <div>
                        <label for="category_id" class="block text-sm font-medium text-gray-300">Category</label>
                        <select name="category_id" id="category_id"
                                class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category_id') === $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <div>
                        <label for="level" class="block text-sm font-medium text-gray-300">Level</label>
                        <select name="level" id="level"
                                class="mt-1 block w-full bg-gray-900 border-gray-600 text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500">
                            <option value="">All Levels</option>
                            <?php $__currentLoopData = $levels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($level); ?>" <?php echo e(request('level') === $level ? 'selected' : ''); ?>>
                                    <?php echo e(ucfirst(str_replace('_', ' ', $level))); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <div class="flex items-end">
                        <button type="submit"
                                class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </div>

                <?php if(request()->hasAny(['search', 'status', 'category_id', 'level'])): ?>
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-gray-400">
                            Showing <?php echo e($courses->count()); ?> of <?php echo e($courses->total()); ?> courses
                        </p>
                        <a href="<?php echo e(route('instructor.courses.index')); ?>"
                           class="text-sm text-red-500 hover:text-red-400">
                            Clear filters
                        </a>
                    </div>
                <?php endif; ?>
            </form>
        </div>

        <!-- Courses Grid -->
        <?php if($courses->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-gray-800 border border-gray-700 rounded-lg shadow-md overflow-hidden hover:shadow-lg hover:border-red-500 transition-all">
                        <!-- Course Image -->
                        <div class="relative h-48 bg-gray-900">
                            <?php if($course->image): ?>
                                <img src="<?php echo e(Storage::disk('private')->url($course->image)); ?>"
                                     alt="<?php echo e($course->title); ?>"
                                     class="w-full h-full object-cover">
                            <?php else: ?>
                                <div class="w-full h-full flex items-center justify-center">
                                    <i class="fas fa-book text-4xl text-gray-500"></i>
                                </div>
                            <?php endif; ?>

                            <!-- Status Badge -->
                            <div class="absolute top-3 left-3">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    <?php if($course->status === 'published'): ?> bg-green-600 text-green-100
                                    <?php elseif($course->status === 'draft'): ?> bg-yellow-600 text-yellow-100
                                    <?php elseif($course->status === 'under_review'): ?> bg-blue-600 text-blue-100
                                    <?php else: ?> bg-gray-600 text-gray-100 <?php endif; ?>">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $course->status))); ?>

                                </span>
                            </div>

                            <?php if($course->featured): ?>
                                <div class="absolute top-3 right-3">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-600 text-red-100">
                                        Featured
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Course Content -->
                        <div class="p-6">
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">
                                    <?php echo e($course->title); ?>

                                </h3>
                                <?php if($course->subtitle): ?>
                                    <p class="text-sm text-gray-400 line-clamp-2"><?php echo e($course->subtitle); ?></p>
                                <?php endif; ?>
                            </div>

                            <!-- Course Stats -->
                            <div class="grid grid-cols-2 gap-4 mb-4 text-sm text-gray-400">
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-2"></i>
                                    <?php echo e($course->enrollments_count); ?> students
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-book-open mr-2"></i>
                                    <?php echo e($course->chapters_count); ?> chapters
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-play-circle mr-2"></i>
                                    <?php echo e($course->lectures_count); ?> lectures
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-star mr-2"></i>
                                    <?php echo e(number_format($course->average_rating ?: 0, 1)); ?> rating
                                </div>
                            </div>

                            <!-- Price -->
                            <div class="mb-4">
                                <?php if($course->hasDiscount()): ?>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-500 line-through">$<?php echo e(number_format($course->original_price, 2, '.', '')); ?></span>
                                        <span class="text-lg font-bold text-red-400 ml-2">$<?php echo e(number_format($course->price, 2, '.', '')); ?></span>
                                        <span class="bg-red-500 text-white text-xs px-1 py-0.5 rounded ml-1"><?php echo e($course->getDiscountPercentage()); ?>% OFF</span>
                                    </div>
                                <?php elseif($course->isFree()): ?>
                                    <span class="text-lg font-bold text-green-400">Free</span>
                                <?php else: ?>
                                    <span class="text-lg font-bold text-white">$<?php echo e(number_format($course->price, 2, '.', '')); ?></span>
                                <?php endif; ?>
                            </div>

                            <!-- Actions -->
                            <div class="flex space-x-2">
                                <a href="<?php echo e(route('instructor.course-builder.show', $course)); ?>"
                                   class="flex-1 bg-red-600 hover:bg-red-700 text-white text-center px-3 py-2 rounded text-sm font-medium transition-colors">
                                    <i class="fas fa-edit mr-1"></i>Course Builder
                                </a>
                                <a href="<?php echo e(route('instructor.courses.show', $course)); ?>"
                                   class="bg-gray-600 hover:bg-gray-700 text-white text-center px-3 py-2 rounded text-sm font-medium transition-colors"
                                   title="View course details (redirects to Course Builder)">
                                    <i class="fas fa-eye mr-1"></i>Details
                                </a>
                                <div class="relative">
                                    <button type="button"
                                            class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-2 rounded text-sm font-medium transition-colors dropdown-toggle"
                                            data-course-slug="<?php echo e($course->slug); ?>">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu absolute right-0 bottom-full mb-2 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-50 hidden">
                                        <div class="py-1">
                                            <form method="POST" action="<?php echo e(route('instructor.courses.duplicate', $course)); ?>" class="block">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                                                    <i class="fas fa-copy mr-2"></i>Duplicate
                                                </button>
                                            </form>
                                            <button type="button"
                                                    onclick="toggleCourseStatus('<?php echo e($course->slug); ?>')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                                                <i class="fas fa-<?php echo e($course->status === 'published' ? 'eye-slash' : 'eye'); ?> mr-2"></i>
                                                <?php echo e($course->status === 'published' ? 'Unpublish' : 'Publish'); ?>

                                            </button>
                                            <?php if($course->enrollments_count === 0): ?>
                                                <button type="button"
                                                        onclick="deleteCourse('<?php echo e($course->slug); ?>')"
                                                        class="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700">
                                                    <i class="fas fa-trash mr-2"></i>Delete
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="flex justify-center">
                <?php echo e($courses->links()); ?>

            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-12 text-center">
                <i class="fas fa-book text-6xl text-gray-500 mb-6"></i>
                <h3 class="text-xl font-medium text-white mb-4">No courses found</h3>
                <?php if(request()->hasAny(['search', 'status', 'category_id', 'level'])): ?>
                    <p class="text-gray-400 mb-6">Try adjusting your filters or search terms.</p>
                    <a href="<?php echo e(route('instructor.courses.index')); ?>"
                       class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Clear Filters
                    </a>
                <?php else: ?>
                    <p class="text-gray-400 mb-6">You haven't created any courses yet. Start by creating your first course!</p>
                    <button onclick="showCreateCourseModal()"
                       class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors inline-flex items-center gap-2">
                        <i class="fas fa-plus"></i>Create Your First Course
                    </button>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Create Course Modal -->
<div id="create-course-modal" class="fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border border-gray-700 w-96 shadow-lg rounded-md bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-white">Create New Course</h3>
                <button onclick="hideCreateCourseModal()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="create-course-form" action="<?php echo e(route('instructor.courses.create-and-build')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="mb-4">
                    <label for="course-title" class="block text-sm font-medium text-gray-300 mb-2">Course Title</label>
                    <input type="text" id="course-title" name="title" required
                           class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                           placeholder="Enter your course title">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideCreateCourseModal()"
                            class="px-4 py-2 bg-gray-700 text-gray-300 text-base font-medium rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                        Create Course
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border border-gray-700 w-96 shadow-lg rounded-md bg-gray-800">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-900">
                <i class="fas fa-exclamation-triangle text-red-400"></i>
            </div>
            <h3 class="text-lg font-medium text-white mt-4">Delete Course</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-400">
                    Are you sure you want to delete this course? This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirm-delete"
                        class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                    Delete
                </button>
                <button id="cancel-delete"
                        class="px-4 py-2 bg-gray-700 text-gray-300 text-base font-medium rounded-md w-24 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dropdown toggles
    document.querySelectorAll('.dropdown-toggle').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const dropdown = this.nextElementSibling;
            
            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdown) {
                    menu.classList.add('hidden');
                }
            });
            
            dropdown.classList.toggle('hidden');
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
    });

    // Delete modal
    const deleteModal = document.getElementById('delete-modal');
    const confirmDelete = document.getElementById('confirm-delete');
    const cancelDelete = document.getElementById('cancel-delete');
    let courseToDelete = null;

    window.deleteCourse = function(courseSlug) {
        courseToDelete = courseSlug;
        deleteModal.classList.remove('hidden');
    };

    confirmDelete.addEventListener('click', function() {
        if (courseToDelete) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `<?php echo e(route('instructor.courses.index')); ?>/${courseToDelete}`;
            form.innerHTML = `
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });

    cancelDelete.addEventListener('click', function() {
        deleteModal.classList.add('hidden');
        courseToDelete = null;
    });

    // Toggle course status
    window.toggleCourseStatus = function(courseSlug) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo e(url('instructor/courses')); ?>/${courseSlug}/toggle-status`;
        form.innerHTML = `
            <?php echo csrf_field(); ?>
            <?php echo method_field('PATCH'); ?>
        `;
        document.body.appendChild(form);
        form.submit();
    };

    // Create course modal functions
    window.showCreateCourseModal = function() {
        document.getElementById('create-course-modal').classList.remove('hidden');
        document.getElementById('course-title').focus();
    };

    window.hideCreateCourseModal = function() {
        document.getElementById('create-course-modal').classList.add('hidden');
        document.getElementById('course-title').value = '';
    };
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/instructor/courses/index.blade.php ENDPATH**/ ?>