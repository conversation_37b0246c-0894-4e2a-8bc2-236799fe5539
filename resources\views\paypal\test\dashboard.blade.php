@extends('layouts.app')

@section('title', 'PayPal Testing Dashboard - Escape Matrix Academy')

@section('content')
<div class="py-12 bg-black min-h-screen">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">PayPal Testing Dashboard</h1>
                    <p class="text-gray-400">Sandbox environment for testing PayPal integration</p>
                </div>
                <div class="bg-yellow-900 border border-yellow-700 rounded-lg px-4 py-2">
                    <span class="text-yellow-300 font-medium">🧪 SANDBOX MODE</span>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Total Payments</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['total_payments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Completed</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['completed_payments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Pending</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['pending_payments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-red-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Failed</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['failed_payments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Revenue</p>
                        <p class="text-2xl font-bold text-white">${{ number_format($stats['total_revenue'], 2) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing Tools -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- API Testing -->
            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                <h3 class="text-xl font-semibold text-white mb-4">API Testing</h3>
                
                <div class="space-y-4">
                    <button onclick="testConnection()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors">
                        Test PayPal Connection
                    </button>
                    
                    <button onclick="getConfiguration()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition-colors">
                        View Configuration
                    </button>
                    
                    <button onclick="generateWebhookPayload()" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition-colors">
                        Generate Webhook Payload
                    </button>
                </div>

                <div id="apiResults" class="mt-4 p-3 bg-gray-900 rounded text-sm text-gray-300 hidden">
                    <pre id="apiResultsContent"></pre>
                </div>
            </div>

            <!-- Payment Testing -->
            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                <h3 class="text-xl font-semibold text-white mb-4">Payment Testing</h3>
                
                <form id="testPaymentForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Test Course</label>
                        <select name="course_id" required class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                            <option value="">Select a course</option>
                            @foreach($testCourses as $course)
                                <option value="{{ $course->id }}">${{ number_format($course->price, 2, '.', '') }} - {{ $course->title }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Custom Amount (Optional)</label>
                        <input type="number" name="amount" step="0.01" min="0.01" max="10000" 
                               placeholder="Leave empty to use course price" 
                               class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                    </div>
                    
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded transition-colors">
                        Create Test Payment
                    </button>
                </form>

                <div id="paymentResults" class="mt-4 p-3 bg-gray-900 rounded text-sm text-gray-300 hidden">
                    <pre id="paymentResultsContent"></pre>
                </div>
            </div>
        </div>

        <!-- Webhook Testing -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8">
            <h3 class="text-xl font-semibold text-white mb-4">Webhook Testing</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Payment ID</label>
                    <select id="webhookPaymentId" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="">Select payment</option>
                        @foreach($recentPayments as $payment)
                            <option value="{{ $payment->id }}">{{ $payment->id }} - {{ $payment->course->title }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Event Type</label>
                    <select id="webhookEventType" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="PAYMENT.CAPTURE.COMPLETED">Payment Completed</option>
                        <option value="PAYMENT.CAPTURE.DENIED">Payment Denied</option>
                        <option value="PAYMENT.CAPTURE.DECLINED">Payment Declined</option>
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button onclick="simulateWebhook()" class="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded transition-colors">
                        Simulate Webhook
                    </button>
                </div>
            </div>

            <div id="webhookResults" class="mt-4 p-3 bg-gray-900 rounded text-sm text-gray-300 hidden">
                <pre id="webhookResultsContent"></pre>
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-semibold text-white">Recent Test Payments</h3>
                <button onclick="clearTestData()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm transition-colors">
                    Clear Test Data
                </button>
            </div>
            
            @if($recentPayments->count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="text-gray-400 border-b border-gray-700">
                            <tr>
                                <th class="text-left py-2">ID</th>
                                <th class="text-left py-2">User</th>
                                <th class="text-left py-2">Course</th>
                                <th class="text-left py-2">Amount</th>
                                <th class="text-left py-2">Status</th>
                                <th class="text-left py-2">Created</th>
                                <th class="text-left py-2">PayPal ID</th>
                            </tr>
                        </thead>
                        <tbody class="text-white">
                            @foreach($recentPayments as $payment)
                                <tr class="border-b border-gray-700">
                                    <td class="py-2 font-mono text-xs">{{ Str::limit($payment->id, 8) }}</td>
                                    <td class="py-2">{{ $payment->user->name }}</td>
                                    <td class="py-2">{{ Str::limit($payment->course->title, 30) }}</td>
                                    <td class="py-2">${{ number_format($payment->amount, 2) }}</td>
                                    <td class="py-2">
                                        <span class="px-2 py-1 rounded text-xs
                                            @if($payment->status === 'completed') bg-green-900 text-green-300
                                            @elseif($payment->status === 'pending') bg-yellow-900 text-yellow-300
                                            @elseif($payment->status === 'failed') bg-red-900 text-red-300
                                            @else bg-gray-900 text-gray-300 @endif">
                                            {{ ucfirst($payment->status) }}
                                        </span>
                                    </td>
                                    <td class="py-2">{{ $payment->created_at->format('M j, g:i A') }}</td>
                                    <td class="py-2 font-mono text-xs">{{ Str::limit($payment->payment_id, 15) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-8 text-gray-400">
                    <p>No test payments yet. Create a test payment to get started.</p>
                </div>
            @endif
        </div>

        <!-- PayPal Sandbox Info -->
        <div class="bg-blue-900 border border-blue-700 rounded-lg p-6">
            <h3 class="text-xl font-semibold text-blue-300 mb-4">PayPal Sandbox Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-blue-200">
                <div>
                    <h4 class="font-medium mb-2">Test Credit Cards</h4>
                    <ul class="text-sm space-y-1">
                        <li><strong>Visa:</strong> ****************</li>
                        <li><strong>Mastercard:</strong> ****************</li>
                        <li><strong>Amex:</strong> ***************</li>
                        <li><strong>CVV:</strong> Any 3-4 digits</li>
                        <li><strong>Expiry:</strong> Any future date</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Test PayPal Accounts</h4>
                    <ul class="text-sm space-y-1">
                        <li><strong>Buyer:</strong> <EMAIL></li>
                        <li><strong>Password:</strong> password123</li>
                        <li><strong>Merchant:</strong> <EMAIL></li>
                        <li><strong>Password:</strong> password123</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// API Testing Functions
async function testConnection() {
    showApiResults('Testing PayPal connection...');
    try {
        const response = await fetch('/paypal/test/connection');
        const data = await response.json();
        showApiResults(JSON.stringify(data, null, 2));
    } catch (error) {
        showApiResults('Error: ' + error.message);
    }
}

async function getConfiguration() {
    showApiResults('Loading configuration...');
    try {
        const response = await fetch('/paypal/test/configuration');
        const data = await response.json();
        showApiResults(JSON.stringify(data, null, 2));
    } catch (error) {
        showApiResults('Error: ' + error.message);
    }
}

async function generateWebhookPayload() {
    showApiResults('Generating webhook payload...');
    try {
        const response = await fetch('/paypal/test/webhook-payload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                event_type: 'PAYMENT.CAPTURE.COMPLETED'
            })
        });
        const data = await response.json();
        showApiResults(JSON.stringify(data, null, 2));
    } catch (error) {
        showApiResults('Error: ' + error.message);
    }
}

// Payment Testing
document.getElementById('testPaymentForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    showPaymentResults('Creating test payment...');
    
    const formData = new FormData(this);
    try {
        const response = await fetch('/paypal/test/payment', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });
        const data = await response.json();
        showPaymentResults(JSON.stringify(data, null, 2));
        
        if (data.success && data.approval_url) {
            if (confirm('Test payment created! Open PayPal approval URL?')) {
                window.open(data.approval_url, '_blank');
            }
        }
    } catch (error) {
        showPaymentResults('Error: ' + error.message);
    }
});

// Webhook Testing
async function simulateWebhook() {
    const paymentId = document.getElementById('webhookPaymentId').value;
    const eventType = document.getElementById('webhookEventType').value;
    
    if (!paymentId) {
        alert('Please select a payment ID');
        return;
    }
    
    showWebhookResults('Simulating webhook...');
    try {
        const response = await fetch('/paypal/test/webhook', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                payment_id: paymentId,
                event_type: eventType
            })
        });
        const data = await response.json();
        showWebhookResults(JSON.stringify(data, null, 2));
    } catch (error) {
        showWebhookResults('Error: ' + error.message);
    }
}

// Clear Test Data
async function clearTestData() {
    if (!confirm('Are you sure you want to clear all test payment data? This cannot be undone.')) {
        return;
    }
    
    try {
        const response = await fetch('/paypal/test/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                confirm: 'yes'
            })
        });
        const data = await response.json();
        alert(data.message);
        if (data.success) {
            location.reload();
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
}

// Helper functions
function showApiResults(content) {
    document.getElementById('apiResults').classList.remove('hidden');
    document.getElementById('apiResultsContent').textContent = content;
}

function showPaymentResults(content) {
    document.getElementById('paymentResults').classList.remove('hidden');
    document.getElementById('paymentResultsContent').textContent = content;
}

function showWebhookResults(content) {
    document.getElementById('webhookResults').classList.remove('hidden');
    document.getElementById('webhookResultsContent').textContent = content;
}
</script>
@endsection
