@extends('layouts.app')

@section('title', 'Session Expired - Escape Matrix Academy')
@section('description', 'Your session has expired for security reasons. Please refresh the page and try again.')

@section('content')
<section class="min-h-screen flex items-center justify-center py-20 bg-gradient-to-br from-black via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Error Code -->
            <div class="mb-8">
                <h1 class="text-8xl md:text-9xl font-bold text-red-500 mb-4">419</h1>
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Session Expired</h2>
                <p class="text-xl text-gray-400 mb-8">
                    Your session has expired for security reasons. Please refresh the page and try again.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button onclick="window.location.reload()" class="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh Page
                </button>
                
                @auth
                    <!-- For authenticated users -->
                    <a href="{{ route('courses.index') }}" class="border border-gray-600 text-white hover:bg-gray-800 px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-book mr-2"></i>
                        Browse Courses
                    </a>
                @else
                    <!-- For unauthenticated users -->
                    <a href="{{ route('login') }}" class="border border-gray-600 text-white hover:bg-gray-800 px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login
                    </a>
                @endauth
            </div>

            <!-- Back to Home -->
            <div class="text-center">
                <a href="{{ route('home') }}" class="text-gray-400 hover:text-white transition-colors inline-flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </div>
</section>
@endsection