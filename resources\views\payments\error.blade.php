@extends('layouts.app')

@section('title', 'Payment Error - Escape Matrix Academy')

@section('content')
<!-- Payment Error Hero Section -->
<section class="py-20 bg-gradient-to-br from-red-900 via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Error Icon -->
            <div class="mb-8">
                <div class="w-24 h-24 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    @if(session('error_type') === 'account_restricted')
                        <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    @else
                        <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    @endif
                </div>
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    @if(session('error_type') === 'account_restricted')
                        Payment System Temporarily Unavailable
                    @else
                        Payment Could Not Be Completed
                    @endif
                </h1>
                <p class="text-xl text-gray-300 mb-8">
                    We encountered an issue processing your payment. Don't worry - no charges have been made to your account.
                </p>
            </div>

            <!-- Error Details Card -->
            <div class="bg-gray-800 border border-red-700 rounded-lg p-8 mb-8 text-left">
                <h2 class="text-2xl font-bold text-white mb-6 text-center">What Happened?</h2>
                
                @if(isset($error) && $error)
                    <div class="bg-red-900/50 border border-red-700 rounded-lg p-4 mb-6">
                        <div class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h3 class="text-red-400 font-semibold mb-1">Error Details</h3>
                                <p class="text-red-300 text-sm">{{ $error }}</p>
                                @if(session('debug_id'))
                                    <p class="text-red-400 text-xs mt-2">
                                        Reference ID: {{ session('debug_id') }}
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                @if(session('error_type') === 'account_restricted')
                    <!-- Special message for account restriction -->
                    <div class="bg-yellow-900/50 border border-yellow-700 rounded-lg p-4 mb-6">
                        <div class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h3 class="text-yellow-400 font-semibold mb-1">System Status Update</h3>
                                <p class="text-yellow-300 text-sm">We're experiencing a temporary issue with our payment system. Our team has been automatically notified and is working to resolve this immediately.</p>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="space-y-4 text-gray-300">
                    <div class="flex items-start space-x-3">
                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p><strong>Payment was not processed:</strong> Your payment method was not charged.</p>
                    </div>
                    <div class="flex items-start space-x-3">
                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p><strong>Your account is safe:</strong> No unauthorized transactions occurred.</p>
                    </div>
                    <div class="flex items-start space-x-3">
                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <p><strong>You can try again:</strong> The course is still available for purchase.</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @if(isset($course) && $course && session('is_retryable') && !session('error_type') === 'account_restricted')
                    <a href="{{ route('courses.show', $course) }}" 
                       class="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Try Payment Again</span>
                    </a>
                @endif
                
                <a href="{{ route('courses.index') }}" 
                   class="border border-gray-600 text-gray-300 hover:bg-gray-700 px-8 py-4 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0l-4 4m4-4l-4-4"></path>
                    </svg>
                    <span>Browse Courses</span>
                </a>
                
                @if(session('requires_support') || session('error_type') === 'account_restricted')
                    <a href="mailto:<EMAIL>?subject=Payment Issue - {{ session('debug_id', 'Unknown') }}" 
                       class="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>Contact Support (Recommended)</span>
                    </a>
                @else
                    <a href="{{ route('contact') }}" 
                       class="border border-gray-600 text-gray-300 hover:bg-gray-700 px-8 py-4 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>Contact Support</span>
                    </a>
                @endif
            </div>
            
            @if(session('error_type') === 'account_restricted')
                <div class="mt-8 pt-6 border-t border-gray-700">
                    <p class="text-gray-400 text-sm">
                        We apologize for the inconvenience. You can bookmark this course and try again later, or contact support for immediate assistance.
                    </p>
                </div>
            @endif
        </div>
    </div>
</section>

<!-- Troubleshooting Section -->
<section class="py-16 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-3xl font-bold text-white mb-12 text-center">Common Solutions</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-2">Check Payment Method</h3>
                            <p class="text-gray-400">Ensure your PayPal account has sufficient funds or a valid payment method linked.</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-2">Verify Account Status</h3>
                            <p class="text-gray-400">Make sure your PayPal account is verified and in good standing.</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-2">Try Again Later</h3>
                            <p class="text-gray-400">Sometimes temporary issues resolve themselves. Wait a few minutes and try again.</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-2">Contact Support</h3>
                            <p class="text-gray-400">If the problem persists, our support team is here to help you complete your purchase.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
